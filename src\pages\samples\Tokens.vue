<script setup lang="ts">
	/**
	 * @file Sample Page: User data.
	 * @version 1.0.0
	 * @since 1.0.0
	 * <AUTHOR> <<EMAIL>>
	 * @license Copyright (c) 2022 Canon Canada Inc.
	 */

    /**
     * -------
     * Imports
     * -------
     */

    import { useAppStore } from '@/stores/AppStore';
    import getAccessToken from '@/composables/auth/getAccessToken';
    import { onMounted, ref } from 'vue';
    import { CanonAuth } from '@/lib/canonAuth';

    /**
     * ----
     * Main
     * ----
     */

    // Add stores.
    const appStore = useAppStore();

    let tokenMSGraph_NoScope = ref();
    let tokenMSGraph_NoScope_Decoded_Header = ref();
    let tokenMSGraph_NoScope_Decoded_Payload = ref();

    let tokenMSGraph_UserRead = ref();
    let tokenMSGraph_UserRead_Decoded_Header = ref();
    let tokenMSGraph_UserRead_Decoded_Payload = ref();

    let tokenCustom_Generic = ref();
    let tokenCustom_Generic_Decoded_Header = ref();
    let tokenCustom_Generic_Decoded_Payload = ref();
    
    let tokenCustom_Specific = ref();
    let tokenCustom_Specific_Decoded_Header = ref();
    let tokenCustom_Specific_Decoded_Payload = ref();

    // Get a no scope defined token - Returns all MS Graph scopes.
    const msGraphNoScope = async () =>
    {
        tokenMSGraph_NoScope.value = await getAccessToken( false );

        const { header, payload }  = CanonAuth.decodeToken( tokenMSGraph_NoScope.value );

        tokenMSGraph_NoScope_Decoded_Header.value = JSON.stringify( header, null, 2 );
        tokenMSGraph_NoScope_Decoded_Payload.value = JSON.stringify( payload, null, 2 );
    }

    // Get a no scope defined token - Returns all MS Graph scopes.
    const msGraphUserRead = async () =>
    {
        tokenMSGraph_UserRead.value = await getAccessToken( false, ['User.Read'] );

        const { header, payload }  = CanonAuth.decodeToken( tokenMSGraph_UserRead.value );

        tokenMSGraph_UserRead_Decoded_Header.value = JSON.stringify( header, null, 2 );
        tokenMSGraph_UserRead_Decoded_Payload.value = JSON.stringify( payload, null, 2 );
    }

    // Get a generic (.default) scope access token. Returns all custom scopes.
    const customGeneric = async () =>
    {
        tokenCustom_Generic.value = await getAccessToken( false, ['api://f31ae01c-1723-4908-aa36-5cf2caff0158/.default'] );

        const { header, payload }  = CanonAuth.decodeToken( tokenCustom_Generic.value );

        tokenCustom_Generic_Decoded_Header.value = JSON.stringify( header, null, 2 );
        tokenCustom_Generic_Decoded_Payload.value = JSON.stringify( payload, null, 2 );
    }

    // Get a specific (App.Standard) scope access token.
    const customSpecific = async () =>
    {
        tokenCustom_Specific.value = await getAccessToken( false, ['api://f31ae01c-1723-4908-aa36-5cf2caff0158/App.Standard'] );

        const { header, payload }  = CanonAuth.decodeToken( tokenCustom_Specific.value );

        tokenCustom_Specific_Decoded_Header.value = JSON.stringify( header, null, 2 );
        tokenCustom_Specific_Decoded_Payload.value = JSON.stringify( payload, null, 2 );
    }

    // Executes after Vue template is loaded to DOM. Required on all pages.
    // Extend the below functionality to add additional page initializations as needed.
    onMounted ( async () =>
    {
        // Required on -ALL- pages to stop page loader.
        appStore.stopPageLoader();
    });
</script>

<template>
	<v-container class="pt-8">
		<v-row class="text-center">
			<v-col cols="12">
				<span class="text-h3">Tokens</span>
			</v-col>
		</v-row>

		<v-row class="text-left">
			<v-col cols="12">
                <v-sheet>
                    <p>This page shows the two different types of tokens you can request from Microsoft Entra ID (Azure).</p>

                    <v-card class="mx-auto my-8" elevation="16">
                        <v-card-item>
                        <v-card-title>
                            MS Graph Token - No Scope
                        </v-card-title>
                            <v-card-subtitle>
                                Request a token that has no scopes defined. This will return a MS Graph scoped only token.
                            </v-card-subtitle>
                        </v-card-item>

                        <v-card-text>
                            <v-row>
                                <v-col>
                                    <v-btn @click="msGraphNoScope()" density="default">Get Token</v-btn>
                                </v-col>
                            </v-row>

                            <v-row v-if="tokenMSGraph_NoScope">
                                <v-col>
                                    <v-textarea label="Access Token Value (Encoded)" :model-value="tokenMSGraph_NoScope" variant="filled" auto-grow density="default"></v-textarea>
                                </v-col>
                            </v-row>

                            <v-row v-if="tokenMSGraph_NoScope_Decoded_Header">
                                <v-col>
                                    <v-textarea label="Header (Decoded)" :model-value="tokenMSGraph_NoScope_Decoded_Header" variant="filled" auto-grow density="default"></v-textarea>
                                </v-col>
                            </v-row>

                            <v-row v-if="tokenMSGraph_NoScope_Decoded_Payload">
                                <v-col>
                                    <span class="text-h5 text-warning">NOTE!</span>
                                    <p>This token was requested with no specified scopes. This returns a token only used for MS Graph APIs.</p><br>
                                    <v-textarea label="Payload (Decoded)" :model-value="tokenMSGraph_NoScope_Decoded_Payload" variant="filled" auto-grow density="default"></v-textarea>
                                </v-col>
                            </v-row>
                        </v-card-text>
                    </v-card>

                    <v-card class="mx-auto my-8" elevation="16">
                        <v-card-item>
                        <v-card-title>
                            MS Graph Token - Specific Scope
                        </v-card-title>
                            <v-card-subtitle>
                                Request a token with the scope "User.Read".
                            </v-card-subtitle>
                        </v-card-item>

                        <v-card-text>
                            <v-row>
                                <v-col>
                                    <v-btn @click="msGraphUserRead()" density="default">Get Token</v-btn>
                                </v-col>
                            </v-row>

                            <v-row v-if="tokenMSGraph_NoScope">
                                <v-col>
                                    <v-textarea label="Access Token Value (Encoded)" :model-value="tokenMSGraph_UserRead" variant="filled" auto-grow density="default"></v-textarea>
                                </v-col>
                            </v-row>

                            <v-row v-if="tokenMSGraph_UserRead_Decoded_Header">
                                <v-col>
                                    <v-textarea label="Header (Decoded)" :model-value="tokenMSGraph_UserRead_Decoded_Header" variant="filled" auto-grow density="default"></v-textarea>
                                </v-col>
                            </v-row>

                            <v-row v-if="tokenMSGraph_UserRead_Decoded_Payload">
                                <v-col>
                                    <span class="text-h5 text-warning">NOTE!</span>
                                    <p>Even though you requested a token with one scope, you got back all of them! This is normal when requesting MS Graph tokens.</p><br>
                                    <v-textarea label="Payload (Decoded)" :model-value="tokenMSGraph_UserRead_Decoded_Payload" variant="filled" auto-grow density="default"></v-textarea>
                                </v-col>
                            </v-row>
                        </v-card-text>
                    </v-card>

                    <v-card class="mx-auto my-8" elevation="16">
                        <v-card-item>
                        <v-card-title>
                            Custom Scope - Default
                        </v-card-title>
                            <v-card-subtitle>
                                Request a scope with the /.Default parameter. This is a generic "get all custom scopes" request.
                            </v-card-subtitle>
                        </v-card-item>

                        <v-card-text>
                            <v-row>
                                <v-col>
                                    <v-btn @click="customGeneric()" density="default">Get Token</v-btn>
                                </v-col>
                            </v-row>

                            <v-row v-if="tokenCustom_Generic">
                                <v-col>
                                    <v-textarea label="Access Token Value (Encoded)" :model-value="tokenCustom_Generic" variant="filled" auto-grow density="default"></v-textarea>
                                </v-col>
                            </v-row>

                            <v-row v-if="tokenCustom_Generic_Decoded_Header">
                                <v-col>
                                    <v-textarea label="Header (Decoded)" :model-value="tokenCustom_Generic_Decoded_Header" variant="filled" auto-grow density="default"></v-textarea>
                                </v-col>
                            </v-row>

                            <v-row v-if="tokenCustom_Generic_Decoded_Payload">
                                <v-col>
                                    <v-textarea label="Payload (Decoded)" :model-value="tokenCustom_Generic_Decoded_Payload" variant="filled" auto-grow density="default"></v-textarea>
                                </v-col>
                            </v-row>
                        </v-card-text>
                    </v-card>

                    <v-card class="mx-auto my-8" elevation="16">
                        <v-card-item>
                        <v-card-title>
                            Custom Scope - Specific
                        </v-card-title>
                            <v-card-subtitle>
                                Request a custom scope called "App.Standard".
                            </v-card-subtitle>
                        </v-card-item>

                        <v-card-text>
                            <v-row>
                                <v-col>
                                    <v-btn @click="customSpecific()" density="default">Get Token</v-btn>
                                </v-col>
                            </v-row>

                            <v-row v-if="tokenCustom_Specific">
                                <v-col>
                                    <v-textarea label="Access Token Value (Encoded)" :model-value="tokenCustom_Specific" variant="filled" auto-grow density="default"></v-textarea>
                                </v-col>
                            </v-row>

                            <v-row v-if="tokenCustom_Specific_Decoded_Header">
                                <v-col>
                                    <v-textarea label="Header (Decoded)" :model-value="tokenCustom_Specific_Decoded_Header" variant="filled" auto-grow density="default"></v-textarea>
                                </v-col>
                            </v-row>

                            <v-row v-if="tokenCustom_Specific_Decoded_Payload">
                                <v-col>
                                    <span class="text-h5 text-warning">NOTE!</span>
                                    <p>Even though you requested a token with one scope, you got back all of them! This is normal when requesting custom scopes.</p><br>
                                    <v-textarea label="Payload (Decoded)" :model-value="tokenCustom_Specific_Decoded_Payload" variant="filled" auto-grow density="default"></v-textarea>
                                </v-col>
                            </v-row>
                        </v-card-text>
                    </v-card>
                </v-sheet>
			</v-col>
		</v-row>
	</v-container>
</template>

<style lang="scss"></style>