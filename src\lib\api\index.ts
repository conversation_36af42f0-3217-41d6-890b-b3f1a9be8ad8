/**
 * @file Includes all API calls to backend.
 * @version 1.0.0
 * @since 1.0.0
 * <AUTHOR> <<EMAIL>>
 * @@license Copyright (c) 2024 Canon Canada Inc.
 */

/**
 * -------
 * Imports
 * -------
 */

import type { AxiosResponse, AxiosInstance } from 'axios';
import type { 
    Accessory,
    AgreementListResponse,
    FilterParams,
    LegalAgreement,
    PostLoginRequest, 
    PostLoginResponse,
    UpdateUserProfileRequest
} from './types.ts';

import axios from 'axios';
import getAccessToken from '@/composables/auth/getAccessToken.js';
import type { DeviceAgreement, DeviceAgreementAssetsPayload, Employee, MSGraphUser, UserAgreementPayload } from '@/lib/common/types.js';

/**
 * ----
 * Main
 * ----
 */

// Create an Axios instance with the base URL and default headers.
const api : AxiosInstance = axios.create
({
    baseURL : import.meta.env.VITE_APP_API_BASE_URL,
    headers :
    {
        'Content-Type': 'application/json',
    }
});

/**
 * -------
 * Exports
 * -------
 */

/**
 * Sends request that indicates a user has logged in.
 *
 * @param {PostLoginRequest} loginData
 * @return {*}  {Promise <AxiosResponse <PostLoginResponse> >}
 */

export const postLogin = (
    loginData: PostLoginRequest, 
    additionalHeaders?: Record<string, string>
): Promise<AxiosResponse<PostLoginResponse>> => {
    const formdata = new FormData();
    formdata.append('idToken',loginData.idToken)
    return api.post<PostLoginResponse>(
        '/user', 
        formdata,
        {
            headers: {
                ...api.defaults.headers.common,
                ...additionalHeaders
            }
        }
    );
};

/**
 * Sends request to update users profile details.
 *
 * @param {UpdateUserProfileRequest} profileData
 * @return {*}  {Promise <AxiosResponse <void> >}
 */
export  const  updateUserProfile = async ( microsoftAccountId : string, profileData: UpdateUserProfileRequest ): Promise <AxiosResponse <PostLoginResponse>> =>
    {
        const token=await getAccessToken( [import.meta.env.VITE_APP_API_SCOPE],false );
        const additionalHeaders = {
            'Authorization': `Bearer ${token}`
        };
        return api.patch<PostLoginResponse>( `/user/${ microsoftAccountId }`, profileData,   {
            headers: {
                ...api.defaults.headers.common,
                ...additionalHeaders
            }
        });
    };

    export const fetchAccessories = async (): Promise<AxiosResponse<Accessory[]>> => {
        const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
        const additionalHeaders = {
          Authorization: `Bearer ${token}`,
        };
        return api.get<Accessory[]>('/asset/accessories', {
          headers: {
            ...api.defaults.headers.common,
            ...additionalHeaders,
          },
        });
      };
      
      export const fetchAccessoryById = async (id: number): Promise<AxiosResponse<Accessory>> => {
        const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
        const additionalHeaders = {
          Authorization: `Bearer ${token}`,
        };
        return api.get<Accessory>(`asset/accessory/byId/${id}`, {
          headers: {
            ...api.defaults.headers.common,
            ...additionalHeaders,
          },
        });
      };
      
      export const createAccessory = async (data: Accessory): Promise<AxiosResponse<Accessory>> => {
        const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
        const additionalHeaders = {
          Authorization: `Bearer ${token}`,
        };
        return api.post<Accessory>('asset/accessory', data, {
          headers: {
            ...api.defaults.headers.common,
            ...additionalHeaders,
          },
        });
      };
      
      export const updateAccessory = async (data: Accessory): Promise<AxiosResponse<Accessory>> => {
        const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
        const additionalHeaders = {
          Authorization: `Bearer ${token}`,
        };
        return api.patch<Accessory>('asset/accessory', data, {
          headers: {
            ...api.defaults.headers.common,
            ...additionalHeaders,
          },
        });
      };
      
      export const deleteAccessory = async (id: number): Promise<AxiosResponse<void>> => {
        const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
        const additionalHeaders = {
          Authorization: `Bearer ${token}`,
        };
        return api.delete<void>(`asset/accessory/byId/${id}`, {
          headers: {
            ...api.defaults.headers.common,
            ...additionalHeaders,
          },
        });
      };
/**
 * Fetch all legal agreements
 */
export const fetchLegalAgreements = async (): Promise<AxiosResponse<LegalAgreement[]>> => {
  const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
  const additionalHeaders = {
    Authorization: `Bearer ${token}`,
  };

  return api.get<LegalAgreement[]>(`legal/`, {
    headers: {
      ...api.defaults.headers.common,
      ...additionalHeaders,
    },
  });
};

    /**
 * Sends request to retrive Canon address locations.
 *

 * @return {*}  {Promise <AxiosResponse <LocationDetails> >}
 */
    export const getAgreementList = async (params: FilterParams): Promise<AxiosResponse<AgreementListResponse>> => {
  const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
  const additionalHeaders = {
    'Authorization': `Bearer ${token}`
  };
  const { page, size, sort } = params;
  const payload = {
    globalSearch: params.globalSearch || '',
    filters: params.filters || []
  }
  const rquestUrl = `/deviceAgreement/pagination?page=${page || 0}&size=${size || 2}&sort=${sort || ''}`

  return api.post<AgreementListResponse>(rquestUrl, payload, {
    headers: {
      ...api.defaults.headers.common,
      ...additionalHeaders
    }
  });
};

    /**
 * Sends request to retrive Canon address locations.
 *

 * @return {*}  {Promise <AxiosResponse <LocationDetails> >}
 */
    export  const  saveUserAgreement = async (payload:UserAgreementPayload): Promise <AxiosResponse <any>> =>
      {
          const token=await getAccessToken( [import.meta.env.VITE_APP_API_SCOPE],false );
          const additionalHeaders = {
              'Authorization': `Bearer ${token}`
          };
          
          return api.post<any>( `/deviceAgreement`,payload,   {
              headers: {
                  ...api.defaults.headers.common,
                  ...additionalHeaders
              }
          });
      };
      export  const  updateAgreement = async (payload:DeviceAgreementAssetsPayload): Promise <AxiosResponse <DeviceAgreement>> =>
        {
            const token=await getAccessToken( [import.meta.env.VITE_APP_API_SCOPE],false );
            const additionalHeaders = {
                'Authorization': `Bearer ${token}`
            };
            
            return api.patch<DeviceAgreement>( `/deviceAgreement`,payload,   {
                headers: {
                    ...api.defaults.headers.common,
                    ...additionalHeaders
                }
            });
        };

      
     /** Sends request to retrive Canon address locations.
      *
     
      * @return {*}  {Promise <AxiosResponse <LocationDetails> >}
      */
         export  const  getAgreementByOId = async (oId:string): Promise <AxiosResponse <any>> =>
           {
               const token=await getAccessToken( [import.meta.env.VITE_APP_API_SCOPE],false );
               const additionalHeaders = {
                   'Authorization': `Bearer ${token}`
               };
               return api.get<any>( `/deviceAgreement/user/${oId}`,   {
                   headers: {
                       ...api.defaults.headers.common,
                       ...additionalHeaders
                   }
               });
           };


              /** Sends request to retrive Canon address locations.
      *
     
      * @return {*}  {Promise <AxiosResponse <LocationDetails> >}
      */
         export  const  checkUserDetails = async (payload:any): Promise <AxiosResponse <any>> =>
          {
              const token=await getAccessToken( [import.meta.env.VITE_APP_API_SCOPE],false );
              const additionalHeaders = {
                  'Authorization': `Bearer ${token}`
              };
              return api.post<any>( `/user/admin/addUser`, payload,  {
                  headers: {
                      ...api.defaults.headers.common,
                      ...additionalHeaders
                  }
              });
          };

               /** Sends request to retrive Canon address locations.
      *
     
      * @return {*}  {Promise <AxiosResponse <LocationDetails> >}
      */
         export  const  gerUserWiseTrackAssets = async (empCode:string): Promise <AxiosResponse <any>> =>
          {
              const token=await getAccessToken( [import.meta.env.VITE_APP_API_SCOPE],false );
              const additionalHeaders = {
                  'Authorization': `Bearer ${token}`
              };
              return api.get<any>( `/user/wisetrack/${empCode}`,   {
                  headers: {
                      ...api.defaults.headers.common,
                      ...additionalHeaders
                  }
              });
          };
          /** 
 * Retrieves a device agreement by its ID
 * 
 * @param {number} deviceAgreementId - The ID of the device agreement
 * @return {Promise<AxiosResponse<any>>}
 */
export const getAgreementByDeviceId = async (deviceAgreementId: number): Promise<AxiosResponse<any>> => {
  const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
  const additionalHeaders = {
      'Authorization': `Bearer ${token}`
  };
  return api.get<any>(`/deviceAgreement/byId/${deviceAgreementId}`, {
      headers: {
          ...api.defaults.headers.common,
          ...additionalHeaders
      }
  });
} ;

export const getAgreementByMicrsoftId = async (deviceAgreementId: number): Promise<AxiosResponse<any>> => {
  const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
  const additionalHeaders = {
      'Authorization': `Bearer ${token}`
  };
  return api.get<any>(`/deviceAgreement/byId/${deviceAgreementId}`, {
      headers: {
          ...api.defaults.headers.common,
          ...additionalHeaders
      }
  });
} ;
/**
 * Fetches user details from Microsoft Graph API by Microsoft Account ID
 * 
 * @param {string} microsoftAccountId - The Microsoft Account ID of the user
 * @return {Promise<AxiosResponse<MSGraphUser>>} - The user details
 */
export const getUserDetailsByMicrosoftId = async (microsoftAccountId: string): Promise<AxiosResponse<Employee>> => {
  const token = await getAccessToken(undefined, true);
  
  const response = await fetch(
    `https://graph.microsoft.com/v1.0/users/${microsoftAccountId}?$select=id,displayName,mail,mailNickname,jobTitle,mobilePhone,department,employeeId,address,office,manager&$expand=manager`,
    {
      headers: {
        Authorization: `Bearer ${token}`
      }
    }
  );
  
  const data = await response.json();
  return { data, status: response.status } as AxiosResponse<Employee>;
};

/**
 * Fetches a WiseTrack asset by barcode
 * @param barcode The barcode of the asset to fetch
 * @returns Promise with the asset data
 */
export const getWiseTrackAssetByBarcode = async (barcode: string): Promise<AxiosResponse<any>> => {
  const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
  
  try {
    const response = await api.get<any>(`/asset/wisetrack/${barcode}`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    
    return response;
  } catch (error) {
    console.error('Error fetching WiseTrack asset by barcode:', error);
    throw error;
  }
};

export const agreementEventLog = async (deviceAgreementId: string): Promise<AxiosResponse<any>> => {
  const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
  
  try {
    const response = await api.get<any>(`/deviceAgreement/byId/${deviceAgreementId}/eventLog`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    
    return response;
  } catch (error) {
    console.error('Error fetching WiseTrack asset by barcode:', error);
    throw error;
  }
};

// Export types for ease of importing.
export type { 
    PostLoginRequest,
    PostLoginResponse,
    UpdateUserProfileRequest
} ;

/**
 * Gets a legal agreement by its ID
 * 
 * @param {number} legalId - The ID of the legal agreement
 * @return {Promise<AxiosResponse<LegalAgreement>>}
 */
export const getLegalAgreementById = async (legalId: number): Promise<AxiosResponse<LegalAgreement>> => {
  const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
  const additionalHeaders = {
    'Authorization': `Bearer ${token}`
  };
  return api.get<LegalAgreement>(`/legal/byId/${legalId}`, {
    headers: {
      ...api.defaults.headers.common,
      ...additionalHeaders
    }
  });
};

/**
 * Creates a new legal agreement
 * 
 * @param {LegalAgreement} data - The legal agreement data
 * @return {Promise<AxiosResponse<LegalAgreement>>}
 */
export const saveLegalAgreement = async (data: LegalAgreement): Promise<AxiosResponse<LegalAgreement>> => {
  const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
  const additionalHeaders = {
    'Authorization': `Bearer ${token}`
  };
  return api.post<LegalAgreement>('/legal', data, {
    headers: {
      ...api.defaults.headers.common,
      ...additionalHeaders
    }
  });
};

/**
 * Creates a new legal agreement
 * 
 * @param {LegalAgreement} data - The legal agreement data
 * @return {Promise<AxiosResponse<LegalAgreement>>}
 */
export const createLeagalDocClone = async (data: LegalAgreement): Promise<AxiosResponse<LegalAgreement>> => {
  const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
  const additionalHeaders = {
    'Authorization': `Bearer ${token}`
  };
  return api.post<LegalAgreement>('/legal/clone', data, {
    headers: {
      ...api.defaults.headers.common,
      ...additionalHeaders
    }
  });
};

/**
 * Updates an existing legal agreement
 * 
 * @param {LegalAgreement} data - The legal agreement data with updated fields
 * @return {Promise<AxiosResponse<LegalAgreement>>}
 */
export const updateLegalAgreement = async (data: LegalAgreement): Promise<AxiosResponse<LegalAgreement>> => {
  const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
  const additionalHeaders = {
    'Authorization': `Bearer ${token}`
  };
  return api.patch<LegalAgreement>('/legal', data, {
    headers: {
      ...api.defaults.headers.common,
      ...additionalHeaders
    }
  });
};

/**
 * Deletes a legal agreement by its ID
 *
 * @param {number} legalId - The ID of the legal agreement to delete
 * @return {Promise<AxiosResponse<void>>}
 */
export const deleteLegalAgreement = async (legalId: number): Promise<AxiosResponse<void>> => {
  const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
  const additionalHeaders = {
    'Authorization': `Bearer ${token}`
  };
  return api.delete<void>(`/legal/byId/${legalId}`, {
    headers: {
      ...api.defaults.headers.common,
      ...additionalHeaders
    }
  });
};

/**
 * Gets legal agreements by legalTypeId
 *
 * @param {number} legalTypeId - The legalTypeId to fetch agreements for
 * @return {Promise<AxiosResponse<LegalAgreement[]>>}
 */
export const getLegalAgreementsByTypeId = async (legalTypeId: number): Promise<AxiosResponse<LegalAgreement[]>> => {

  const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
  const additionalHeaders = {
    'Authorization': `Bearer ${token}`
  };
  return api.get<LegalAgreement[]>(`/legal/byLegalTypeId/${legalTypeId}`, {
    headers: {
      ...api.defaults.headers.common,
      ...additionalHeaders
    }
  });
  
  
};


 /* Sends file to save on server which in return given unique identifer 
 * which needs to be sent when user clicks on save.
 *

 * @return {*}  {Promise <AxiosResponse <LocationDetails> >}
*/
export const uploadFile = async (formData:FormData): Promise <AxiosResponse <string>> => {
    const token=await getAccessToken( [import.meta.env.VITE_APP_API_SCOPE],false );
    const additionalHeaders = {
        'Authorization': `Bearer ${token}`
    };
    return api.post<string>( `/legal/upload`,formData,{
        headers: {
            ...api.defaults.headers.common,
            ...additionalHeaders
        }
    });
};
