# Device Agreement System - Components & Services Documentation

## Table of Contents
1. [Overview](#overview)
2. [Core Components](#core-components)
3. [Agreement Components](#agreement-components)
4. [Legal Components](#legal-components)
5. [Pages](#pages)
6. [State Management (Stores)](#state-management-stores)
7. [Services & Libraries](#services--libraries)
8. [Composables](#composables)
9. [Views & Routing](#views--routing)
10. [Internationalization](#internationalization)

---

## Overview

The Device Agreement System is a comprehensive Vue.js application built for managing device agreements in an enterprise environment. It provides role-based access control, multilingual support (English/French), and integration with Microsoft services for authentication and user management.

**Key Technologies:**
- Vue 3 with Composition API
- TypeScript
- Vuetify 3 (Material Design)
- Pinia (State Management)
- Vue Router
- Vue i18n (Internationalization)
- Microsoft Entra ID (Azure AD) Authentication
- Axios (HTTP Client)

---

## Core Components

### AppHeader.vue
**Purpose:** Main application header with navigation, user menu, and language switcher  
**Features:** Displays Canon logo and app title, responsive design with drawer navigation for mobile

### AppFooter.vue
**Purpose:** Application footer component with copyright and additional links  
**Features:** Consistent branding and legal information display

### AppLoader.vue
**Purpose:** Global loading spinner component that shows during API calls and page transitions  
**Features:** Queue-based loading system with custom messages and smooth animations

### AppMessage.vue
**Purpose:** Full-screen message overlay for critical system messages and error states  
**Features:** Blocks user interaction during critical operations, customizable buttons and actions

### AppSnackbar.vue
**Purpose:** Toast notification component for success/error messages  
**Features:** Customizable colors, icons, timeout settings, and positioning options

### UserMenu.vue
**Purpose:** Dropdown menu for user profile, theme switching, language selection, and logout  
**Features:** Role-based menu items, theme toggle, language switcher, and secure logout

### ConfirmDialog.vue
**Purpose:** Reusable confirmation dialog for delete operations and critical actions  
**Features:** Customizable title, message, and button text with callback support

### AlertBox.vue
**Purpose:** Simple alert dialog component for displaying informational messages  
**Features:** Basic modal with customizable title, message, and button text

### AccessoriesAdd.vue
**Purpose:** Form dialog for adding/editing accessories with validation  
**Features:** Bilingual input fields, form validation, and CRUD operations

### HelpDeskDialog.vue
**Purpose:** Dialog for users to report issues with textarea input  
**Features:** Form validation, issue categorization, and submission handling

---

## Agreement Components

### EventLog.vue
**Purpose:** Timeline component displaying agreement history with formatted dates  
**Features:** Chronological event display, bullet points for event details, localized date formatting

---

## Legal Components

### LegalAgreementForm.vue
**Purpose:** Complex form for creating/editing legal agreements  
**Features:** File upload support, content validation, bilingual content management, mutual exclusivity between file and text content

---

## Pages

### Home.vue
**Purpose:** Landing page with authentication check and role-based navigation  
**Features:** Automatic routing based on user roles, login integration, welcome messaging

### DAList.vue
**Purpose:** Device agreement list with filtering, pagination, and table management  
**Features:** Advanced filtering, sorting, search functionality, and bulk operations

### DARequestForm.vue
**Purpose:** Main form for creating/editing device agreements  
**Features:** Asset and accessory management, barcode scanning, employee selection, legal agreement assignment

### ViewAgreement.vue
**Purpose:** Agreement viewing page with approval workflow and status management  
**Features:** Document viewing, approval actions, status tracking, and event logging

### AccessoriesList.vue
**Purpose:** CRUD interface for managing accessories  
**Features:** Custom pagination, validation, bilingual support, and status management

### LegalDocs.vue
**Purpose:** Legal document management with expandable rows and version control  
**Features:** Document versioning, file management, and hierarchical display

### 404.vue
**Purpose:** Error page for handling not found routes  
**Features:** User-friendly error messaging and navigation options

---

## State Management (Stores)

### AppStore.ts
**Purpose:** Global app state managing themes, languages, loaders, and initialization  
**Features:** Theme persistence, language switching, loading queue management, and app lifecycle

### UserStore.ts
**Purpose:** User authentication state with Microsoft Graph profile data  
**Features:** Session management, user profile storage, and authentication status tracking

### MessageStore.ts
**Purpose:** Queue-based message system for displaying full-screen alerts  
**Features:** Message queuing, priority handling, and application state control

### SnackbarStore.ts
**Purpose:** Toast notification state with customizable appearance  
**Features:** Timeout management, positioning control, and color/icon customization

### DialogStore.ts
**Purpose:** Confirmation dialog state management with callback functions  
**Features:** Dynamic dialog content, button customization, and action callbacks

### InternalUserStore.ts
**Purpose:** Internal user profile data with roles and permissions  
**Features:** Role management, approval status tracking, and user metadata storage

### TableLoaderStore.ts
**Purpose:** Data table loading states with pagination management  
**Features:** Page size control, loading indicators, and pagination state

---

## Services & Libraries

### API Services (src/lib/api/)

#### index.ts
**Purpose:** Centralized API client with authentication headers  
**Features:** Automatic token injection, request/response interceptors, and all backend endpoint calls

#### types.ts
**Purpose:** TypeScript interfaces and types for API requests and responses  
**Features:** Type safety, IntelliSense support, and contract definitions

### Authentication (src/lib/canonAuth/)

#### CanonAuth.ts
**Purpose:** Microsoft Entra ID (Azure AD) authentication wrapper  
**Features:** SSO support, token management, silent authentication, and session handling

#### CanonAuthGuard.ts
**Purpose:** Route guard for protecting pages based on authentication and roles  
**Features:** Role-based access control, automatic redirects, and permission validation

#### CanonAuthNavigation.ts
**Purpose:** Navigation helper for handling authenticated routing  
**Features:** Secure navigation, role-based redirects, and route protection

### Utilities

#### Logger.ts
**Purpose:** Development logging utility with conditional console output  
**Features:** Environment-based logging, structured output, and debugging support

#### msGraph/
**Purpose:** Microsoft Graph API integration for user and organizational data  
**Features:** User profile retrieval, organizational hierarchy, and directory services

#### common/types.ts
**Purpose:** Shared TypeScript interfaces and enums  
**Features:** Application-wide type definitions, constants, and shared contracts

---

## Composables

### auth/userLoginProcess.ts
**Purpose:** Login workflow composable handling authentication flow  
**Features:** Multi-step authentication, error handling, and user setup

### auth/userLogoutProcess.ts
**Purpose:** Logout workflow composable managing session cleanup  
**Features:** Secure logout, cache clearing, and redirection handling

### auth/actionPermission.ts
**Purpose:** Role-based permission checking for UI elements and routes  
**Features:** Granular permissions, role validation, and access control

### auth/getAccessToken.ts
**Purpose:** Token acquisition helper for API calls  
**Features:** Automatic refresh, scope management, and error handling

---

## Views & Routing

### MainView.vue
**Purpose:** Main layout wrapper with header, footer, and router outlet  
**Features:** Consistent layout, navigation integration, and responsive design

### FullView.vue
**Purpose:** Full-screen layout for error pages and standalone components  
**Features:** Clean layout without navigation, suitable for error states

### router/index.ts
**Purpose:** Vue Router configuration with role-based guards  
**Features:** Nested routes, lazy loading, role-based protection, and meta information

---

## Internationalization

### en-US.json
**Purpose:** English translations for all UI text and system notifications  
**Features:** Comprehensive text coverage, validation messages, and user interface labels

### fr-CA.json
**Purpose:** Canadian French translations maintaining consistency  
**Features:** Complete translation coverage, cultural adaptation, and consistent terminology

---

## Architecture Benefits

1. **Modular Design:** Clear separation of concerns with dedicated components for specific functionality
2. **Type Safety:** Comprehensive TypeScript implementation for better development experience
3. **Scalability:** Well-structured state management and component architecture
4. **Security:** Role-based access control and secure authentication integration
5. **Internationalization:** Full bilingual support for Canadian market requirements
6. **User Experience:** Consistent UI patterns and responsive design
7. **Maintainability:** Clear documentation, consistent naming conventions, and modular structure

---

*This documentation provides a comprehensive overview of the Device Agreement System's architecture and components. Each component is designed to work together seamlessly while maintaining independence and reusability.*
