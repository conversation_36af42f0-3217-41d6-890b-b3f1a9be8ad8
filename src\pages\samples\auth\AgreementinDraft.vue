<template>
  <div class="agreement-wrapper">
    <div class="name">{{ name }}</div>
    <div class="role">{{ role }}</div>
    <div class="divider"></div>
    <div class="status-box">
      {{ statusMessage }}
    </div>
  </div>
</template>

<script setup>
import { defineProps } from 'vue';

const statusMessage = "This agreement is currently being prepared.";

const props = defineProps({
  name: {
    type: String,
    required: true
  },
  role: {
    type: String,
    required: true
  }
});
</script>

<style scoped>
.agreement-wrapper {
  padding: 2rem;
  text-align: center;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.name {
  font-size: 1.8rem;
  font-weight: 600;
  letter-spacing: 1px;
  color: #111;
}

.role {
  margin-top: 0.3rem;
  font-size: 1rem;
  color: #444;
}

.divider {
  margin: 1rem auto;
  width: 100px;
  height: 5px;
  background-color: #386f8f;
}

.status-box {
  margin-top: 1.5rem;
  color: (--v-theme-warning);
  padding: 1rem;
  font-size: 1.1rem;
  border-radius: 3px;
  display: inline-block;
}
</style>
