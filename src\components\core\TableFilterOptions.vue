<template>
    <v-row justify="end"  >
      <v-col cols="6"  md="4" lg="5" sm="8" class="text-center" >
        <v-text-field variant="outlined" class="custom-select"
        prepend-inner-icon="search" density="compact" 
        v-model="globalSearch" placeholder="Search" clearable
        @keydown.enter="handleEnter"
        @click:clear="handleEnter"></v-text-field>

      </v-col>
 

    <v-col cols="3" md="3" lg="3" sm="2" class="text-left"  >

 
    <v-menu  v-model="menu" :close-on-content-click="false" location="start">
      <template v-slot:activator="{ props }">
        <v-badge  v-if="activeFilters.length > 0" color="success" :content="activeFilters.length">
          <v-btn :slim="!mdAndUp" color="primary" v-bind="props" flat  prepend-icon="filter">
            <span v-if="mdAndUp">
              Filters
            </span>
          </v-btn>
        </v-badge>
        <v-badge v-else color="transparent" >
          <v-btn color="primary" :slim="!mdAndUp" v-bind="props" flat  prepend-icon="filter">
            <span v-if="mdAndUp">
              Filters
            </span>
          </v-btn>
        </v-badge>
      </template>
      <v-form ref="formRef">
        <v-card min-width="300">
          <v-list>
            <v-list-item>
              <v-select 
                density="compact" 
                v-model="columnName" 
                :rules="columnNameRules" 
                label="Select Column" 
                item-title="translatedTitle"
                item-value="key"
                :items="translatedColumns"
                return-object
              >
              </v-select>
            </v-list-item>

            <v-list-item>
              <v-select density="compact" v-model="selectedfilterType" :rules="selectedfilterTypeRules" label="Select Filter"
                :items=filterTypes item-title="title" item-value="key" variant="solo"
                :disabled="columnName?.key === 'status'"></v-select>
            </v-list-item>

            <v-list-item>
              <v-text-field density="compact" v-model="valueforMatch" :rules="valueforMatchRules" label="Value"
                variant="solo" v-if="columnName?.key !== 'status'"></v-text-field>
  
              <v-select density="compact" v-model="valueforMatch" :rules="valueforMatchRules" label="Status"
                :items="statusOptions" item-title="title" item-value="value" variant="solo"
                v-if="columnName?.key === 'status'"></v-select>
            </v-list-item>
          </v-list>

          <v-card-actions>
            <div class="filter-btns-container">
            <v-btn left variant="text" @click="clearAllFilters">
              Clear All
            </v-btn>
            <v-btn right color="primary" variant="text"  @click="applyFilter" :disabled="isFormInValid">
              Apply
            </v-btn>
          </div>
          </v-card-actions>

        </v-card>
      </v-form>
      <v-card v-if="activeFilters.length" class="mx-auto" min-width="300">
        <v-toolbar color="secondary" class="px-5">

          <div >Active Filters({{ activeFilters.length }})</div>

          <v-spacer></v-spacer>

        </v-toolbar>

        <v-list>

          <v-list-item v-for="filter in activeFilters" :key="filter.filterColumn"
            :title="t(filter.filterColumn.title) + `: ` + filter.filterType.title + `: ` + 
            (filter.filterColumn.key === 'status' ? 
              ApprovalStatusDescription[Number(filter.filterValue) as ApprovalStatus] : 
              filter.filterValue)">
            <template v-slot:append>
              <v-btn @click=editFitler(filter.filterColumn) color="grey-lighten-1" icon="edit"
              variant="plain"></v-btn>
              <v-btn @click=removeFilter(filter.filterColumn) color="grey-lighten-1" icon="close"
                variant="plain"></v-btn>
            </template>
          </v-list-item>
        </v-list>
      </v-card>

    </v-menu>
    
</v-col>
<v-col v-if="actionPermissions( CN_Action.ADMIN )" cols="3" md="5" lg="4" sm="2"   class="text-left" >

    
    <v-btn  color="primary" prepend-icon="add" :to="{ name: 'newRequest' }">
         <span v-if="mdAndUp">
              {{t('component.DaList.createNew')}}
            </span>
      
    </v-btn>
</v-col>
  </v-row>


  <!-- </div> -->


</template>

<script setup lang="ts">
import { useSnackbarStore } from '@/stores/SnackbarStore';
import { useTableLoader } from '@/stores/TableLoaderStore';
import { ref, watch,computed  } from 'vue';
import { useDisplay } from 'vuetify';
import { useI18n } from 'vue-i18n';
    import actionPermissions from '@/composables/auth/actionPermission';

import { ApprovalStatus, ApprovalStatusDescription, CN_Action } from '@/lib/common/types';
const tableLoading = useTableLoader();

const { mdAndUp } = useDisplay()
const { t } = useI18n();

const menu = ref(false);
const globalSearch = ref('');
let columns = ref([]) as any;
let activeFilters = ref([]) as any;
let filterTypes = ref([{ key: 'contains', title: 'Contains' },
{ key: 'equals_to', title: 'Equals To' },
{ key: 'not_equals_to', title: 'Not Equals To' }]) as any;

const formRef = ref<any>(null);
const valueforMatch = ref('');
const valueforMatchRules = [
  (v: string) => !!v || 'Value is required',
];
const selectedfilterType = ref('');
const selectedfilterTypeRules = [
  (v: string) => !!v || 'Select Filter Type',
];
const columnName = ref();
const columnNameRules = [
  (v: string) => !!v || 'Select Column for Filter',
];
const props = defineProps({
  data: {
    type: Object,
    required: true
  }
});

const emitter = defineEmits(['update:search', 'update:filters', 'update:triggerDownload']);

const handleEnter = (() => {
    // emit the entered value by user to the parent
    emitter('update:search', globalSearch.value);
    tableLoading.showLoading()

  
})

const isFormInValid = computed(() => !columnName.value || !valueforMatch.value || !selectedfilterType.value);

const applyFilter = () => {
  formRef.value.validate().then((status: any) => {
    if (status.valid) {
      const selectedColumnValue = columns.value.find((col: any) => col.key == (columnName.value as any)?.key)
      let selectedfilterValue = selectedfilterType.value;
      
      // For status column, we always use equals_to filter
      if (selectedColumnValue.key === 'status') {
        selectedfilterValue = 'equals_to';
      }
      
      const selectedfilter = filterTypes.value.find((col: any) => col.key == selectedfilterValue)
      const values = {
        filterValue: valueforMatch.value,
        filterColumn: selectedColumnValue,
        filterType: selectedfilter,
        translatedTitle: t(selectedColumnValue.title)
      }
      // checks for colums if already exists
      const columnExists = activeFilters.value.some((filter: any) => filter.filterColumn === selectedColumnValue);

      if (!columnExists) {
        activeFilters.value.push(values);

      }
      // In else case we need to modify that existing filter.
      else {
        // finds the column with the modified values
        const column = activeFilters.value.find((filter: any) => filter.filterColumn === selectedColumnValue);
        column.filterValue=valueforMatch.value
        column.filterColumn= selectedColumnValue,
        column.filterType= selectedfilter
      }
      resetFrom();
      emitter('update:filters', formFilterForAPI())
      tableLoading.showLoading()
    }
  });
}
const formFilterForAPI = () => {
  // returns an array of filters in a format which is sent to backend to fetch new data
  return activeFilters.value.map((el: any) => {
    return { filterColumn: el.filterColumn.key, filterType: el.filterType.key, filterValue: el.filterValue }
  })
}
const clearAllFilters = () => {
  // hides the filters menu
  menu.value = false;
  // active filters are set to empty 
  activeFilters.value = [];
  resetFrom();
  emitter('update:filters', formFilterForAPI())


}
const resetFrom = () => {
  columnName.value='';
  formRef.value.reset();
}


const editFitler =(item: any)=>{
  const selectedIndex = activeFilters.value.findIndex((col: any) => col.filterColumn.key == item.key)
  columnName.value={...activeFilters.value[selectedIndex].filterColumn,translatedTitle:activeFilters.value[selectedIndex].translatedTitle}
  valueforMatch.value=activeFilters.value[selectedIndex].filterValue
  selectedfilterType.value=activeFilters.value[selectedIndex].filterType.key

  
}
const removeFilter = (item: any) => {
  //finds the index of the item from the list
  const selectedIndex = activeFilters.value.findIndex((col: any) => col.filterColumn.key == item.key)
  //removes the given index  from the list
  activeFilters.value.splice(selectedIndex, 1)
  // new list emitted to the parent component to fetch data with the remainig filters
  emitter('update:filters', formFilterForAPI())

}

const onClear = () => {
  tableLoading.showLoading()
  // emits to globalSearch value to the parent component to fetch data
  emitter('update:search', globalSearch.value);
}
// watch(() => props.resultsCount, (newData) => {
//   if (newData) {
//     console.log(newData)
//   }
// });

// watches the globalsearch value for blank value so onClear method can be triggered
watch(() => globalSearch.value, (newData) => {
  if (!newData) {
    onClear()
  }
});
columns.value = props.data;

// Add this computed property to translate column titles
const translatedColumns = computed(() => {
  return columns.value.map((column: any) => ({
    ...column,
    translatedTitle: t(column.title)
  }));
});

// Add status options for dropdown
const statusOptions = ref([
  { title: ApprovalStatusDescription[ApprovalStatus.Draft], value: ApprovalStatus.Draft.toString() },
  { title: ApprovalStatusDescription[ApprovalStatus.PendingAcceptence], value: ApprovalStatus.PendingAcceptence.toString() },
  { title: ApprovalStatusDescription[ApprovalStatus.Accepted], value: ApprovalStatus.Accepted.toString() },
  { title: ApprovalStatusDescription[ApprovalStatus.Terminated], value: ApprovalStatus.Terminated.toString() }
]);

// Add a watch to set the filter type to 'equals_to' when status is selected
watch(() => columnName.value, (newValue) => {
  if (newValue?.key === 'status') {
    selectedfilterType.value = 'equals_to';
  }
});
</script>
