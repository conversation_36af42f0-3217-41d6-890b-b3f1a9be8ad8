/**
 * @file Types for backend API responses.
 * @version 1.0.0
 * @since 1.0.0
 * <AUTHOR> <<EMAIL>>
 * @@license Copyright (c) 2024 Canon Canada Inc.
 */

import type { DeviceAgreement } from "../common/types";

/**
 * Body interface for a POST login request.
 *
 * @export
 * @interface PostLoginRequest
 */
export interface PostLoginRequest
{
    /**
     * ID Token from MSAL authentication.
     *
     * @type {string}
     * @memberof PostLoginRequest
     */
    idToken : string
}

/**
 * Data interface for a POST login response.
 *
 * @export
 * @interface PostLoginResponse
 */
export interface PostLoginResponse {
    /**
     * User ID.
     *
     * @type {number}
     * @memberof PostLoginResponse
     */
    userId?: number|null;

    /**
     * Microsoft Account ID (GUID).
     *
     * @type {string}
     * @memberof PostLoginResponse
     */
    microsoftAccountId?: string|null;

    /**
     * User's preferred first and last name.
     *
     * @type {string}
     * @memberof PostLoginResponse
     */
    displayName?: string|null;

    /**
     * User's email address.
     *
     * @type {string}
     * @memberof PostLoginResponse
     */
    email?: string|null;

    /**
     * Canon ID associated with the user.
     *
     * @type {string}
     * @memberof PostLoginResponse
     */
    canonId?: string|null;

    /**
     * Status of the user (e.g., Active).
     *
     * @type {string}
     * @memberof PostLoginResponse
     */
    userStatus?: string|null;

    /**
     * User's language preference.
     *
     * @type {string}
     * @memberof PostLoginResponse
     */
    language: string|null;

    /**
     * User's theme preference.
     *
     * @type {string}
     * @memberof PostLoginResponse
     */
    theme: string|null;

    /**
     * User's assigned roles.
     *
     * @type {string[]}
     * @memberof PostLoginResponse
     */
    roles?: string[]|null;

    /**
     * Timestamp of when the user was created.
     *
     * @type {string}
     * @memberof PostLoginResponse
     */
    createdAt?: string|null;

    /**
     * Identifier of the creator.
     *
     * @type {string}
     * @memberof PostLoginResponse
     */
    createdBy?: string|null;

    /**
     * Timestamp of the last update.
     *
     * @type {string}
     * @memberof PostLoginResponse
     */
    updatedAt?: string|null;

    /**
     * Identifier of the person who last updated the user.
     *
     * @type {string}
     * @memberof PostLoginResponse
     */
    updatedBy?: string|null;

    /**
     * Indicates if the user is an approver.
     *
     * @type {boolean}
     * @memberof PostLoginResponse
     */
    approver?: boolean|null;
}


/**
 * Body interface for update user profile request.
 *
 * @export
 * @interface UpdateUserProfileRequest
 */
export interface UpdateUserProfileRequest
{
    /**
     * Users preferred language code.
     *
     * @type {string}
     * @memberof UpdateUserProfileRequest
     */
    language? : string,

    /**
     * Users preferred theme value.
     *
     * @type {string}
     * @memberof UpdateUserProfileRequest
     */
    theme? : string
    
     /**
     * user's manager's microsoft id.
     *
     * @type {string}
     * @memberof UpdateUserProfileRequest
     */
     approverMicrosoftAccountId? : string

       /**
     * Holds value for if user a manager of some one.
     *
     * @type {string}
     * @memberof UpdateUserProfileRequest
     */
       isApprovalFlag? : string
}

export interface TableColumn {
  title: string;
  key: string;
  sortable: boolean;
  width: string;
}

export interface Accessory {
  accessoryId?: number;
  name: string;
  description: string;
  isActive: 'Y' | 'N';
}

export interface LegalVersion {
  legalVersionId: number;
  legalId: number;
  version: number;
  urlEn: string | null;
  urlFr: string | null;
  englishContent: string | null;
  frenchContent: string | null;
  legalBlobEn: string | null;
  legalBlobFr: string | null;
  createdAt: string | null;
  createdBy: string | null;
  updatedAt: string | null;
  updatedBy: string | null;
}

export interface LegalAgreement {
  legalId?: number|null;
  version?: number;
  legalVersion?: number;
  legalAgreementName: string;
  nameEnglish: string;
  nameFrench: string;
  isActive?:string
  createdAt?: string | null;
  createdBy?: string | null;
  updatedAt?: string | null;
  updatedBy?: string | null;
  contentFrench?:string
  contentEnglish?:string
  urlEn?:string
  urlFr?:string
  urlEnglish?:string
  urlFrench?:string
  englishContent?:string
  frenchContent?:string
  legalTypeId?:number
  microsoftAccountId?:string
}

export interface FilterParams {
  page?: number,
  size?: number,
  sort?: string,
  globalSearch?: string,
  value?: string
  filters?:any[]
}

export interface AgreementListResponse {
  daList: DeviceAgreement[];
  numberOfItems: number;
  numberOfPages: number;
}
