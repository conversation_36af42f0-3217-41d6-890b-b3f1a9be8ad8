
<template>

    <v-card class="mb-4 ma-8">
      <v-card-title class="d-flex justify-space-between align-center">
        {{ t('legal.title') }}
        <v-btn 
          color="primary" 
          prepend-icon="add"
          @click="navigateToCreate"
        >
          {{ t('legal.createNew') }}
        </v-btn>
      </v-card-title>
    
    
    <v-data-table
      :headers="headers"
      :items="formattedLegalAgreements"
      item-value="legalId"
      density="compact"
      show-expand
      v-model:expanded="expanded"
      v-model:items-per-page="itemsPerPage"
      v-model:page="currentPage"
    >
     <template v-slot:item.data-table-expand="{ item }">
    <v-icon @click="toggleExpand(item)">
      {{ isExpanded(item) ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}
    </v-icon>
  </template>
      <!-- Actions Column -->
      <template v-slot:item.actions="{ item }">
        <div class="d-flex">
          <v-menu>
            <template v-slot:activator="{ props }">
              <v-btn
                icon
                variant="text"
                v-bind="props"
                @click.stop
              >
                <v-icon>more_vert</v-icon>
              </v-btn>
            </template>
            <v-list>
              <!-- Edit option -->
              <v-list-item @click.stop="editLegalAgreement(item)">
                <template v-slot:prepend>
                  <v-icon color="primary" size="small">edit</v-icon>
                </template>
                <v-list-item-title>{{ t('legal.menu.edit') }}</v-list-item-title>
              </v-list-item>
              
              <!-- Delete option -->
              <v-list-item @click.stop="confirmDeleteLegalAgreement(item)">
                <template v-slot:prepend>
                  <v-icon color="error" size="small">delete</v-icon>
                </template>
                <v-list-item-title>{{ t('legal.menu.delete') }}</v-list-item-title>
              </v-list-item>
              
              <!-- Clone new version option -->
              <v-list-item @click.stop="cloneNewVersion(item)">
                <template v-slot:prepend>
                  <v-icon color="info" size="small">content_copy</v-icon>
                </template>
                <v-list-item-title>{{ t('legal.menu.cloneNewVersion') }}</v-list-item-title>
              </v-list-item>
              
              <!-- Current version details -->
              <v-divider></v-divider>             
              <!-- Current version content -->
              <v-list-item>
                <v-list-item-title class="d-flex align-center">
                  <strong class="mr-1">{{ t('legal.table.version') }} {{ item.legalVersion }}</strong>
                </v-list-item-title>
              </v-list-item>
              
              <!-- English document -->
              <v-list-item v-if="item?.urlEn"  @click="openExistingFile('english',item)" target="_blank">
                <template v-slot:prepend>
                  <v-icon color="primary" size="small">description</v-icon>
                </template>
                <v-list-item-title>{{ t('legal.table.englishDocument') }}</v-list-item-title>
              </v-list-item>
              
              <v-list-item v-else-if="item?.englishContent" @click.stop="openDialog(item?.englishContent || '')">
                <template v-slot:prepend>
                  <v-icon color="primary" size="small">article</v-icon>
                </template>
                <v-list-item-title>{{ t('legal.table.englishDocument') }}</v-list-item-title>
              </v-list-item>
              
              <!-- French document -->
              <v-list-item v-if="item?.urlFr"  @click="openExistingFile('french',item)" target="_blank">
                <template v-slot:prepend>
                  <v-icon color="primary" size="small">description</v-icon>
                </template>
                <v-list-item-title>{{ t('legal.table.frenchDocument') }}</v-list-item-title>
              </v-list-item>
              
              <v-list-item v-else-if="item?.frenchContent" @click.stop="openDialog(item?.frenchContent || '')">
                <template v-slot:prepend>
                  <v-icon color="primary" size="small">article</v-icon>
                </template>
                <v-list-item-title>{{ t('legal.table.frenchDocument') }}</v-list-item-title>
              </v-list-item>
            </v-list>
          </v-menu>
        </div>
      </template>
      
      <!-- Expandable Row - Now only showing older versions -->
      <template v-slot:expanded-row="{ columns, item }">
        <tr>
          <td :colspan="columns.length" class="expanded-row">
            <v-divider class="mb-3"></v-divider>
            <div class="older-versions-container">
              <div v-if="getOlderVersions(item).length > 0">
                <div class="text-subtitle-1 font-weight-medium mb-2">{{ t('legal.table.olderVersions') }}</div>
                <v-data-table
                  :headers="[
                    { title: t('legal.table.version'), key: 'legalVersion' },
                    { title: t('legal.table.agreementName'), key: 'legalAgreementName' },
                    { title: t('legal.table.nameEnglish'), key: 'nameEnglish' },
                    { title: t('legal.table.nameFrench'), key: 'nameFrench' },
                    { title: t('legal.table.createdAt'), key: 'createdAt' },
                    { title: t('legal.table.actions'), key: 'actions', sortable: false }
                  ]"
                  :items="getOlderVersions(item)"
                  density="compact"
                  hide-default-footer
                  class="older-versions-table"
                >
                  <template v-slot:item.createdAt="{ item }">
                    {{ formatDate(item.createdAt || null) }}
                  </template>
                  <template v-slot:item.actions="{ item }">
                    <div class="d-flex justify-center">
                      <v-menu>
                        <template v-slot:activator="{ props }">
                          <v-btn
                            icon
                            variant="text"
                            v-bind="props"
                            @click.stop
                          >
                            <v-icon>more_vert</v-icon>
                          </v-btn>
                        </template>
                        <v-list>
                          <!-- English document -->
                          <v-list-item v-if="item.urlEn" @click="openExistingFile('english',item)" target="_blank">
                            <template v-slot:prepend>
                              <v-icon color="primary" size="small">description</v-icon>
                            </template>
                            <v-list-item-title>{{ t('legal.table.englishDocument') }}</v-list-item-title>
                          </v-list-item>

                          <v-list-item v-else-if="item.englishContent" @click.stop="openDialog(item.englishContent || '')">
                            <template v-slot:prepend>
                              <v-icon color="primary" size="small">article</v-icon>
                            </template>
                            <v-list-item-title>{{ t('legal.table.englishDocument') }}</v-list-item-title>
                          </v-list-item>

                          <!-- French document -->
                          <v-list-item v-if="item.urlFr" @click="openExistingFile('french',item)" target="_blank">
                            <template v-slot:prepend>
                              <v-icon color="primary" size="small">description</v-icon>
                            </template>
                            <v-list-item-title>{{ t('legal.table.frenchDocument') }}</v-list-item-title>
                          </v-list-item>

                          <v-list-item v-else-if="item.frenchContent" @click.stop="openDialog(item.frenchContent || '')">
                            <template v-slot:prepend>
                              <v-icon color="primary" size="small">article</v-icon>
                            </template>
                            <v-list-item-title>{{ t('legal.table.frenchDocument') }}</v-list-item-title>
                          </v-list-item>
                        </v-list>
                      </v-menu>
                    </div>
                  </template>

                </v-data-table>
              </div>
              <div v-else class="text-center pa-4">
                {{ t('legal.table.noOlderVersions') }}
              </div>
            </div>
            <v-divider class="mt-3"></v-divider>
          </td>
        </tr>
      </template>
      <template v-slot:bottom="{ page, pageCount }">
        <div class="custom-footer">
          <!-- Left side: Items per page -->
          <div class="footer-left">
            <span class="items-per-page-text">{{t('generic.itemPerPage')}}</span>
            <v-select
              v-model="itemsPerPage"
              :items="itemsPerPageOptions"
              variant="outlined"
              density="compact"
              hide-details
              class="items-per-page-select"
            ></v-select>
            <span class="items-range-text">
              {{ getFirstItem() }}-{{ getLastItem() }} of {{ formattedLegalAgreements.length }}
            </span>
          </div>

          <!-- Right side: Pagination -->
          <div class="footer-right">
            <v-btn
              icon
              variant="text"
              size="small"
              :disabled="page === 1"
              @click="currentPage = page - 1"
            >
              <v-icon>chevron_left</v-icon>
            </v-btn>
            <span class="page-info">{{ page }} of {{ pageCount }}</span>
            <v-btn
              icon
              variant="text"
              size="small"
              :disabled="page === pageCount"
              @click="currentPage = page + 1"
            >
              <v-icon>chevron_right</v-icon>
            </v-btn>
          </div>
        </div>
      </template>
    </v-data-table>
</v-card>
    <!-- Popup Dialog for Content -->
    <v-dialog v-model="dialog" max-width="500px">
      <v-card>
        <v-card-title>{{ t('legal.dialog.viewContent') }}</v-card-title>
        <v-card-text>{{ fullText }}</v-card-text>
        <v-card-actions>
          <v-btn color="primary" @click="dialog = false">{{ t('legal.dialog.close') }}</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    
    <!-- Confirmation Dialog for Delete -->
    <v-dialog v-model="deleteDialog" max-width="400px">
      <v-card>
        <v-card-title class="text-h6">{{ t('legal.dialog.deleteTitle') }}</v-card-title>
        <v-card-text>
          {{ t('legal.dialog.deleteMessage') }}
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="grey" variant="text" @click="deleteDialog = false">{{ t('legal.dialog.cancel') }}</v-btn>
          <v-btn color="error" variant="text" @click="deleteLegalAgreement">{{ t('legal.dialog.delete') }}</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

</template>

<style scoped>
/* Make expanded row full-width */
.version-column {
  display: flex;
  flex-direction: row;
  gap: 5px;
}

.older-versions-container {
  background-color: rgba(var(--v-theme-background), 0.5);
  border-radius: 4px;
  padding: 12px;
  margin: 0 8px;
}

/* Dark theme adjustments */
:deep(.v-theme--appThemeDark) .older-versions-container {
  background-color: rgba(var(--v-theme-surface), 0.3);
}

.text-truncate {
  cursor: pointer;
  color: blue;
  text-decoration: underline;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
  display: inline-block;
}

.cursor-pointer {
  cursor: pointer;
}

/* Add some spacing in the menu */
:deep(.v-list-item__content) {
  padding: 4px 0;
}

/* Ensure the menu has enough width */
:deep(.v-menu__content) {
  min-width: 250px;
}

/* Styling for older versions table */
.older-versions-table {
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 4px;
}

.older-versions-table :deep(.v-data-table__wrapper) {
  border-radius: 4px;
}
</style>

<script setup lang="ts">
import { fetchLegalAgreements, deleteLegalAgreement as apiDeleteLegalAgreement, getLegalAgreementsByTypeId, getFileByUrl } from '@/lib/api';
import type { LegalAgreement } from '@/lib/api/types';
import { computed, onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import { useSnackbarStore } from '@/stores/SnackbarStore';
import { useAppStore } from '@/stores/AppStore';
import { useI18n } from 'vue-i18n';
import { openBlobFile } from '@/lib/common/utils';

const router = useRouter();
const snackbar = useSnackbarStore();
const appStore = useAppStore();
const { t } = useI18n();
// Pagination variables
const itemsPerPage = ref(25);
const currentPage = ref(1);
const itemsPerPageOptions = [25, 50, 100];
const dialog = ref(false);
const deleteDialog = ref(false);
const fullText = ref("");
const expanded = ref<any[]>([]);
const legalAgreements = ref<LegalAgreement[]>([]);
const expandedRowData = ref<Record<number, LegalAgreement[]>>({});

const formatDate = (dateString: string | null) => {
  if (!dateString) return '';
  return new Date(dateString).toLocaleDateString(appStore.currentLanguage === 'en-US' ?'en-US':'fr-CA', {
    month: 'long',
    day: 'numeric',
    year: 'numeric'
  });
};

const selectedAgreement = ref<LegalAgreement | null>(null);

const openDialog = (text: string) => {
  fullText.value = text;
  dialog.value = true;
};

// Helper functions for pagination display
const getFirstItem = () => {
  return Math.min((currentPage.value - 1) * itemsPerPage.value + 1, formattedLegalAgreements.value.length);
};

const getLastItem = () => {
  return Math.min(currentPage.value * itemsPerPage.value, formattedLegalAgreements.value.length);
};


const navigateToCreate = () => {
  router.push({ name: 'createLegalAgreement' });
};

const editLegalAgreement = (item: LegalAgreement) => {
  router.push({ name: 'editLegalAgreement', params: { id: item.legalId } });
};

const confirmDeleteLegalAgreement = (item: LegalAgreement) => {
  selectedAgreement.value = item;
  deleteDialog.value = true;
};

const deleteLegalAgreement = async () => {
  if (!selectedAgreement.value) return;
  
  try {
    appStore.startLoader('deletingLegalAgreement', t('app.auth.loader.deletingLegalAgreement'));
    
    await apiDeleteLegalAgreement(selectedAgreement.value?.legalId||0);
    
    // Refresh the list
    await loadLegalAgreements();
    
    snackbar.show({
      text: t('legal.messages.deleteSuccess'),
      color: 'success',
      timeout: 3000
    });
  } catch (error:any) {
    console.error("Error deleting legal agreement:", error);
  if (error.response?.data?.errorMessage) {
        snackbar.show({
          text: t(`app.auth.error.${error.response.data.errorMessage}`),
          color: 'error',
          timeout: 5000
        });
        return; // Exit early to prevent further processing
      }
  } finally {
    deleteDialog.value = false;
    selectedAgreement.value = null;
    appStore.stopLoader('deletingLegalAgreement');
  }
};

const loadLegalAgreements = async () => {
  try {
    appStore.startLoader('fetchingLegalAgreements', t('app.auth.loader.fetchingLegalAgreements'));
    
    const response = await fetchLegalAgreements();
    legalAgreements.value = response.data.filter(agreement=>agreement.isActive=='Y');
  } catch (error) {
    console.error("Error fetching data:", error);
    snackbar.show({
      text: t('legal.messages.fetchError'),
      color: 'error',
      timeout: 5000
    });
  } finally {
    appStore.stopLoader('fetchingLegalAgreements');
  }
};

onMounted(() => {
  loadLegalAgreements();
});

const formattedLegalAgreements = computed(() => {
  return legalAgreements.value.map(agreement => ({
    ...agreement,
    createdAt: formatDate(agreement.createdAt || null)
  }));
});
const headers = [
  { title: t('legal.table.version'), key: "legalVersion" },
  { title: t('legal.table.agreementName'), key: "legalAgreementName" },
  { title: t('legal.table.nameEnglish'), key: "nameEnglish" },
  { title: t('legal.table.nameFrench'), key: "nameFrench" },
  { title: t('legal.table.createdAt'), key: "createdAt" },
  { title: t('legal.table.actions'), key: "actions", sortable: false },
  { title: "", key: "data-table-expand" },
];

// Check if a specific item is expanded
const isExpanded = (item: any) => {
  return expanded.value.includes(item.legalId);
};

// Toggle expand state for a specific item
const toggleExpand = async (item: any) => {
  const index = expanded.value.findIndex((id: any) => id === item.legalId);

  if (index === -1) {
    // Expand the item
    expanded.value.push(item.legalId);

    // Fetch data for this legalTypeId if not already loaded
    if (item.legalTypeId && !expandedRowData.value[item.legalTypeId]) {
      try {
        appStore.startLoader('fetchingVersions', t('app.auth.loader.fetchingVersions'));
        const response = await getLegalAgreementsByTypeId(item.legalTypeId);
        expandedRowData.value[item.legalTypeId] = response.data;
      } catch (error) {
        console.error('Error fetching legal agreements by type ID:', error);
        snackbar.show({
          text: t('legal.messages.fetchError'),
          color: 'error',
          timeout: 5000
        });
      } finally {
        appStore.stopLoader('fetchingVersions');
      }
    }
  } else {
    // Collapse the item
    expanded.value.splice(index, 1);
  }
};
// Function to open existing file in new tab
const openExistingFile = async (language: 'english' | 'french',item:LegalAgreement) => {
  const mapper={'english':'en','french':'fr'};
 try {
        appStore.startLoader('loadigFile', t('app.auth.loader.loadingFile'));

    const response = await getFileByUrl(mapper[language],item.legalId);
        appStore.stopLoader('loadigFile');

    // Get the blob from response
const blob = new Blob([response.data], {
  type: 'application/pdf'
});
const url = URL.createObjectURL(blob);

//   if (filenameMatch) {
//     filename = filenameMatch[1];
//   }
// }
    const newWindow = window.open(url, '_blank');
  } catch (error) {
    console.error('Error opening file:', error);
    snackbar.show({
      text: t('legal.messages.fileOpenError'),
      color: 'error',
      timeout: 5000
    });
  }
 
};


// Get older versions of a legal agreement (all except the current one)
const getOlderVersions = (item: LegalAgreement) => {
  if (!item.legalTypeId || !expandedRowData.value[item.legalTypeId]) return [];

  const allVersions = expandedRowData.value[item.legalTypeId];

  // Filter out the current version and sort by version in descending order
  return allVersions
    .filter((v: LegalAgreement) => v.legalVersion !== item.legalVersion)
    .sort((a: LegalAgreement, b: LegalAgreement) => (b.legalVersion || 0) - (a.legalVersion || 0));
};

// Clone a legal agreement to create a new version
const cloneNewVersion = (item: LegalAgreement) => {
  // Navigate to create page with clone parameter
  router.push({ name: 'cloneLegalAgreement', params: { id: item.legalId } });
};
</script>
  
