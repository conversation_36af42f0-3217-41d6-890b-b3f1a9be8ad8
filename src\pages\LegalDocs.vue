
<template>

    <v-card class="mb-4">
      <v-card-title class="d-flex justify-space-between align-center">
        {{ t('legal.title') }}
        <v-btn 
          color="primary" 
          prepend-icon="add"
          @click="navigateToCreate"
        >
          {{ t('legal.createNew') }}
        </v-btn>
      </v-card-title>
    
    
    <v-data-table
      :headers="headers"
      :items="legalAgreements"
      item-value="legalId"
      density="compact"
      show-expand
      v-model:expanded="expanded"
      @click:row="toggleExpand"
    >
      <!-- Actions Column -->
      <template v-slot:item.actions="{ item }">
        <div class="d-flex justify-center">
          <v-menu>
            <template v-slot:activator="{ props }">
              <v-btn
                icon
                variant="text"
                v-bind="props"
                @click.stop
              >
                <v-icon>more_vert</v-icon>
              </v-btn>
            </template>
            <v-list>
              <!-- Edit option -->
              <v-list-item @click.stop="editLegalAgreement(item)">
                <template v-slot:prepend>
                  <v-icon color="primary" size="small">edit</v-icon>
                </template>
                <v-list-item-title>{{ t('legal.menu.edit') }}</v-list-item-title>
              </v-list-item>
              
              <!-- Delete option -->
              <v-list-item @click.stop="confirmDeleteLegalAgreement(item)">
                <template v-slot:prepend>
                  <v-icon color="error" size="small">delete</v-icon>
                </template>
                <v-list-item-title>{{ t('legal.menu.delete') }}</v-list-item-title>
              </v-list-item>
              
              <!-- Clone new version option -->
              <v-list-item @click.stop="cloneNewVersion(item)">
                <template v-slot:prepend>
                  <v-icon color="info" size="small">content_copy</v-icon>
                </template>
                <v-list-item-title>{{ t('legal.menu.cloneNewVersion') }}</v-list-item-title>
              </v-list-item>
              
              <!-- Current version details -->
              <v-divider></v-divider>             
              <!-- Current version content -->
              <v-list-item>
                <v-list-item-title class="d-flex align-center">
                  <strong class="mr-1">{{ t('legal.table.version') }} {{ item.legalVersion }}</strong>
                </v-list-item-title>
              </v-list-item>
              
              <!-- English document -->
              <v-list-item v-if="item?.urlEn" :href="item?.urlEn || undefined" target="_blank">
                <template v-slot:prepend>
                  <v-icon color="primary" size="small">description</v-icon>
                </template>
                <v-list-item-title>{{ t('legal.table.englishDocument') }}</v-list-item-title>
              </v-list-item>
              
              <v-list-item v-else-if="item?.englishContent" @click.stop="openDialog(item?.englishContent || '')">
                <template v-slot:prepend>
                  <v-icon color="primary" size="small">article</v-icon>
                </template>
                <v-list-item-title>{{ t('legal.table.englishDocument') }}</v-list-item-title>
              </v-list-item>
              
              <!-- French document -->
              <v-list-item v-if="item?.urlFr" :href="item?.urlFr|| undefined" target="_blank">
                <template v-slot:prepend>
                  <v-icon color="primary" size="small">description</v-icon>
                </template>
                <v-list-item-title>{{ t('legal.table.frenchDocument') }}</v-list-item-title>
              </v-list-item>
              
              <v-list-item v-else-if="item?.frenchContent" @click.stop="openDialog(item?.frenchContent || '')">
                <template v-slot:prepend>
                  <v-icon color="primary" size="small">article</v-icon>
                </template>
                <v-list-item-title>{{ t('legal.table.frenchDocument') }}</v-list-item-title>
              </v-list-item>
            </v-list>
          </v-menu>
        </div>
      </template>
      
      <!-- Expandable Row - Now only showing older versions -->
      <template v-slot:expanded-row="{ columns, item }">
        <tr>
          <td :colspan="columns.length" class="expanded-row">
            <v-divider class="mb-3"></v-divider>
            <div class="older-versions-container">
              <div v-if="getOlderVersions(item).length > 0">
                <div class="text-subtitle-1 font-weight-medium mb-2">{{ t('legal.table.olderVersions') }}</div>
                <v-list dense>
                  <v-list-item v-for="version in getOlderVersions(item)" :key="version.legalVersionId">
                    <v-data-table
                      :headers="[
                        { title: t('legal.table.id'), key: 'legalId' },
                        { title: t('legal.table.version'), key: 'version' },
                        { title: t('legal.table.versionId'), key: 'legalVersionId' },
                        { title: t('legal.table.actions'), key: 'actions', sortable: false }
                      ]"
                      :items="[version]"
                      density="compact"
                      hide-default-footer
                    >
                      <template v-slot:item.actions="{ item }">
                        <div class="d-flex justify-center">
                          <v-menu>
                            <template v-slot:activator="{ props }">
                              <v-btn
                                icon
                                variant="text"
                                v-bind="props"
                                @click.stop
                              >
                                <v-icon>more_vert</v-icon>
                              </v-btn>
                            </template>
                            <v-list>
                              <!-- English document -->
                              <v-list-item v-if="item.urlEn" :href="item.urlEn || undefined" target="_blank">
                                <template v-slot:prepend>
                                  <v-icon color="primary" size="small">description</v-icon>
                                </template>
                                <v-list-item-title>{{ t('legal.table.englishDocument') }}</v-list-item-title>
                              </v-list-item>
                              
                              <v-list-item v-else-if="item.englishContent" @click.stop="openDialog(item.englishContent || '')">
                                <template v-slot:prepend>
                                  <v-icon color="primary" size="small">article</v-icon>
                                </template>
                                <v-list-item-title>{{ t('legal.table.englishDocument') }}</v-list-item-title>
                              </v-list-item>
                              
                              <!-- French document -->
                              <v-list-item v-if="item.urlFr" :href="item.urlFr || undefined" target="_blank">
                                <template v-slot:prepend>
                                  <v-icon color="primary" size="small">description</v-icon>
                                </template>
                                <v-list-item-title>{{ t('legal.table.frenchDocument') }}</v-list-item-title>
                              </v-list-item>
                              
                              <v-list-item v-else-if="item.frenchContent" @click.stop="openDialog(item.frenchContent || '')">
                                <template v-slot:prepend>
                                  <v-icon color="primary" size="small">article</v-icon>
                                </template>
                                <v-list-item-title>{{ t('legal.table.frenchDocument') }}</v-list-item-title>
                              </v-list-item>
                            </v-list>
                          </v-menu>
                        </div>
                      </template>
                    </v-data-table>
                  </v-list-item>
                </v-list>
              </div>
              <div v-else class="text-center pa-4">
                {{ t('legal.table.noOlderVersions') }}
              </div>
            </div>
            <v-divider class="mt-3"></v-divider>
          </td>
        </tr>
      </template>
    </v-data-table>
</v-card>
    <!-- Popup Dialog for Content -->
    <v-dialog v-model="dialog" max-width="500px">
      <v-card>
        <v-card-title>{{ t('legal.dialog.viewContent') }}</v-card-title>
        <v-card-text>{{ fullText }}</v-card-text>
        <v-card-actions>
          <v-btn color="primary" @click="dialog = false">{{ t('legal.dialog.close') }}</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    
    <!-- Confirmation Dialog for Delete -->
    <v-dialog v-model="deleteDialog" max-width="400px">
      <v-card>
        <v-card-title class="text-h6">{{ t('legal.dialog.deleteTitle') }}</v-card-title>
        <v-card-text>
          {{ t('legal.dialog.deleteMessage') }}
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="grey" variant="text" @click="deleteDialog = false">{{ t('legal.dialog.cancel') }}</v-btn>
          <v-btn color="error" variant="text" @click="deleteLegalAgreement">{{ t('legal.dialog.delete') }}</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

</template>

<style scoped>
/* Make expanded row full-width */
.version-column {
  display: flex;
  flex-direction: row;
  gap: 5px;
}

.older-versions-container {
  background-color: rgba(var(--v-theme-background), 0.5);
  border-radius: 4px;
  padding: 12px;
  margin: 0 8px;
}

/* Dark theme adjustments */
:deep(.v-theme--appThemeDark) .older-versions-container {
  background-color: rgba(var(--v-theme-surface), 0.3);
}

.text-truncate {
  cursor: pointer;
  color: blue;
  text-decoration: underline;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
  display: inline-block;
}

.cursor-pointer {
  cursor: pointer;
}

/* Add some spacing in the menu */
:deep(.v-list-item__content) {
  padding: 4px 0;
}

/* Ensure the menu has enough width */
:deep(.v-menu__content) {
  min-width: 250px;
}
</style>

<script setup lang="ts">
import { fetchLegalAgreements, deleteLegalAgreement as apiDeleteLegalAgreement } from '@/lib/api';
import type { LegalAgreement } from '@/lib/api/types';
import { onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import { useSnackbarStore } from '@/stores/SnackbarStore';
import { useAppStore } from '@/stores/AppStore';
import { useI18n } from 'vue-i18n';

const router = useRouter();
const snackbar = useSnackbarStore();
const appStore = useAppStore();
const { t } = useI18n();

const dialog = ref(false);
const deleteDialog = ref(false);
const fullText = ref("");
const expanded = ref<any[]>([]);
const legalAgreements = ref<LegalAgreement[]>([]);

const formatDate = (dateString: string | null) => {
  if (!dateString) return '';
  return new Date(dateString).toLocaleDateString('en-US', {
    month: '2-digit',
    day: '2-digit',
    year: 'numeric'
  });
};

const selectedAgreement = ref<LegalAgreement | null>(null);

const openDialog = (text: string) => {
  fullText.value = text;
  dialog.value = true;
};

const truncateText = (text: string) => {
  return text.length > 50 ? text.substring(0, 50) + "..." : text;
};

const navigateToCreate = () => {
  router.push({ name: 'createLegalAgreement' });
};

const editLegalAgreement = (item: LegalAgreement) => {
  router.push({ name: 'editLegalAgreement', params: { id: item.legalId } });
};

const confirmDeleteLegalAgreement = (item: LegalAgreement) => {
  selectedAgreement.value = item;
  deleteDialog.value = true;
};

const deleteLegalAgreement = async () => {
  if (!selectedAgreement.value) return;
  
  try {
    appStore.startLoader('deletingLegalAgreement', t('app.auth.loader.deletingLegalAgreement'));
    
    await apiDeleteLegalAgreement(selectedAgreement.value?.legalId||0);
    
    // Refresh the list
    await loadLegalAgreements();
    
    snackbar.show({
      text: t('legal.messages.deleteSuccess'),
      color: 'success',
      timeout: 3000
    });
  } catch (error) {
    console.error("Error deleting legal agreement:", error);
    snackbar.show({
      text: t('legal.messages.deleteError'),
      color: 'error',
      timeout: 5000
    });
  } finally {
    deleteDialog.value = false;
    selectedAgreement.value = null;
    appStore.stopLoader('deletingLegalAgreement');
  }
};

const loadLegalAgreements = async () => {
  try {
    appStore.startLoader('fetchingLegalAgreements', t('app.auth.loader.fetchingLegalAgreements'));
    
    const response = await fetchLegalAgreements();
    legalAgreements.value = response.data.map(agreement => ({
      ...agreement,
      createdAt: formatDate(agreement.createdAt || null)
    })).filter(agreement=>agreement.isActive=='Y');
  } catch (error) {
    console.error("Error fetching data:", error);
    snackbar.show({
      text: t('legal.messages.fetchError'),
      color: 'error',
      timeout: 5000
    });
  } finally {
    appStore.stopLoader('fetchingLegalAgreements');
  }
};

onMounted(() => {
  loadLegalAgreements();
});

const headers = [
  { title: t('legal.table.version'), key: "legalVersion" },
  { title: t('legal.table.agreementName'), key: "legalAgreementName" },
  { title: t('legal.table.nameEnglish'), key: "nameEnglish" },
  { title: t('legal.table.nameFrench'), key: "nameFrench" },
  { title: t('legal.table.createdAt'), key: "createdAt" },
  { title: t('legal.table.actions'), key: "actions", sortable: false },
  { title: "", key: "data-table-expand" },
];

const toggleExpand = (event: any, { item }: any) => {
  const index = expanded.value.findIndex((i: any) => i === item.legalId);
  if (index === -1) {
    expanded.value.push(item.legalId);
  } else {
    expanded.value.splice(index, 1);
  }
};



// Get older versions of a legal agreement (all except the current one)
const getOlderVersions = (item: LegalAgreement) => {
  if (!item.legalVerions || item.legalVerions.length < 1) return [];
  
  // Filter out the current version
  return item.legalVerions
    .filter(v => v.version !== item.legalVersion)
    .sort((a, b) => b.version - a.version); // Sort by version in descending order
};

// Clone a legal agreement to create a new version
const cloneNewVersion = (item: LegalAgreement) => {
  // Navigate to create page with clone parameter
  router.push({ name: 'cloneLegalAgreement', params: { id: item.legalId } });
};
</script>
  
