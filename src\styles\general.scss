/**
 * @file Primary SCSS file with styles that will apply site wide.
 * @version 1.0.0
 * @since 1.0.0
 * <AUTHOR> <<EMAIL>>
 * @license Copyright (c) 2024 Canon Canada Inc.
 */

// Creates a top border (box shadow) on the element with the error colour.
.box-shadow-top-error { box-shadow : inset 0 -1px 0 rgb(var(--v-theme-border)), inset 0 8px 0 rgb(var(--v-theme-error)) !important; }
.box-shadow-top-success { box-shadow : inset 0 -1px 0 rgb(var(--v-theme-border)), inset 0 8px 0 rgb(var(--v-theme-success)) !important; }
.box-shadow-top-warning { box-shadow : inset 0 -1px 0 rgb(var(--v-theme-border)), inset 0 8px 0 rgb(var(--v-theme-warning)) !important; }
.box-shadow-top-info { box-shadow : inset 0 -1px 0 rgb(var(--v-theme-border)), inset 0 8px 0 rgb(var(--v-theme-info)) !important; }

.v-field--disabled
{
    opacity: 1 !important; // Increased from 0.55 to 0.75 for better visibility
}

// Add a specific style for dark theme to improve contrast
.v-theme--appThemeDark {
    .v-field--disabled {
        opacity: 1 !important; // Even higher opacity for dark theme
        
        .v-field__input {
            color: rgba(255, 255, 255, 0.8) !important; // Brighter text in dark theme
        }
    }
}

// Sets theme for custom card (1).
.app-message-card
{
    .v-card-header
    {
        background-color : rgb(var(--v-theme-background-1)) !important;
        color            : rgb(var(--v-theme-on-background-1)) !important;
        box-shadow       : inset 0 -1px 0 rgb(var(--v-theme-border)), inset 0 8px 0 rgb(var(--v-theme-secondary));
        padding          : 12px 24px 4px !important;
    }

    .v-card-actions
    {
        padding : 0px 24px 14px !important;
    }
}

// Overrides border colour of cards when outlined.
.v-card--variant-outlined
{
    border : thin solid rgb(var(--v-theme-border)) !important;
}

.viewport-centre
{
    max-height: calc(100% - 82px);

    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.no-link
{
    text-decoration: none;
    color: inherit;
}

.v-theme--appThemeDark.v-btn--variant-tonal
{
    color: rgb(var(--v-theme-primary-lighten-3)) !important;
}

.v-input--density-compact
{
    .v-field__input
    {
        font-size: 14px !important;
        padding-top: 16px;
    }

    .v-icon
    {
        font-size: 20px !important;
    }

    .v-field--variant-outlined
    {
        .v-field__input
        {
            padding-top: 10px !important;
        }
    }
}

.slim-list
{
    .v-list-item__spacer
    {
        width: 16px !important;
    }

    .v-list-item
    {
        min-height: 35px !important;
    }
}

.unselectable
{
    user-select: none;
    -webkit-user-select: none; /* Safari */
    -moz-user-select: none;    /* Firefox */
    -ms-user-select: none;     /* Internet Explorer/Edge */
}

.slim-dialog
{
    .v-overlay__content
    {
        max-height: calc(100% - 12px);
        width: calc(100% - 12px);
        max-width: calc(100% - 12px);
        margin: 12px;
    }
}
