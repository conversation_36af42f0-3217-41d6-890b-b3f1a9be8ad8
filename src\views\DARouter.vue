<template>
  <router-view />
</template>

<script setup lang="ts">
/**
 * @file Home (welcome) page.
 * @version 1.0.0
 * @since 1.0.0
 * <AUTHOR> 
 * @license Copyright (c) 2024 Canon Canada Inc.
 */

/**
 * -------
 * Imports
 * -------
 */

import { useAppStore } from '@/stores/AppStore';
import { useUserStore } from '@/stores/UserStore';
import { onMounted } from 'vue';
import { useRoute } from 'vue-router';

/**
 * ----
 * Main
 * ----
 */

// Add stores.
const appStore = useAppStore();
const route = useRoute();

onMounted(() => {
  // Check if the current route is the view agreement route
  if (route.name === 'viewRequest' || route.path.includes('/agreements/view/')) {
    // Collapse the sidebar menu
    // appStore.setSidebarCollapsed(true);
  }
  
  appStore.stopPageLoader();
});
</script>
