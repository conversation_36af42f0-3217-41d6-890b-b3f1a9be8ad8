<script setup lang="ts">
import { DeviceAgreementAction, type DeviceAgreement, type DeviceAgreementAssetsPayload, type Employee, type WiseTrackAsset } from '@/lib/common/types';

import { ref, reactive, watch, computed, onMounted } from "vue";
import { useRouter } from "vue-router";
import { CanonAuth } from "@/lib/canonAuth";
import { debounce } from "lodash";
import { fetchAccessories, fetchLegalAgreements, gerUserWiseTrackAssets, getAgreementByOId, saveUserAgreement, updateAgreement, getUserDetailsByMicrosoftId, getWiseTrackAssetByBarcode, checkUserDetails } from '@/lib/api';
import { useAppStore } from '@/stores/AppStore';
import type { Accessory, LegalAgreement } from '@/lib/api/types';
import { useI18n } from 'vue-i18n';
import ConfirmationDialog from '../components/core/ConfirmDialog.vue';
import { useSnackbarStore } from '@/stores/SnackbarStore';
import { useDialogStore } from '@/stores/DialogStore';
import StatusBadge from '@/components/core/StatusBadge.vue';
import EventLog from '@/components/agreement/EventLog.vue';

// Add a ref to control the event log expansion
const eventLogExpanded = ref(false);

// Add a ref to access the EventLog component
const eventLogRef = ref();

// Define props for the component
const props = defineProps({
  mode: {
    type: String,
    default: 'add',
    validator: (value: string) => ['add', 'edit'].includes(value)
  },
  agreementData: {
    type: Object as () => DeviceAgreement | null,
    default: null
  }
});

// Router instance
const router = useRouter();
const loadingEmps=ref(false);
const { t } = useI18n();
const accessories = ref<Accessory[]>([]);
const userAccessories = ref<Accessory[]>([]);

// Computed properties for accessories
const availableAccessories = computed(() => {
  return accessories.value.filter(accessory =>
    !selectedAccessory.value.includes(accessory.accessoryId!)
  );
});

const selectedAccessoriesList = computed(() => {
    return  selectedAccessory.value

  return accessories.value.filter(accessory =>
    selectedAccessory.value.includes(accessory.accessoryId!)
  );
});

// Reactive form state
const form = ref({
  employee: null as Employee | null,
  legalIds: [] as number[],
  comment: '',
  hasEmail: 'Y' as 'Y' | 'N',
  sendNotification: 'Y' as 'Y' | 'N',

});
const snackbar= useSnackbarStore()

const deviceAgreementId = ref<number | null>(null);
  const userName = ref('');

const appStore =useAppStore()
const legalIds = ref<LegalAgreement[]>([]);

const agreementDetails = ref<DeviceAgreement | null>(null);
  const showAssetsSection = ref(false);
// Employee list (fetched from API)
const employees = ref<Employee[]>([]);

const dialogVisible = ref(false);

// Add this ref for validation
const legalAgreementError = ref('');

// Add this ref for WiseTrackAssets
const wiseTrackAssets = ref<WiseTrackAsset[]>([]);

const selectedAccessory = ref<number[]>([]);

// New variables for the updated accessories selection
const accessoryToAdd = ref<number | null>(null);
const accessoryToRemove = ref<Accessory | null>(null);
const showRemoveAccessoryDialog = ref(false);

// Add a ref for tracking removed assets
const removedAssetIds = ref<number[]>([]);

// Add a ref for tracking assets with changed status
const assetsStatusChanged = ref<number[]>([]);
const actionType = ref<'remove' | 'restore' | null>(null);
const assetIndexInConfirm = ref<number |null| undefined>(null);

// Add these computed properties to categorize assets
const activeAssets = computed(() => {
  return wiseTrackAssets.value.filter(asset => 
    !asset.status || asset.status === 'Active'
  );
});

const removedAssets = computed(() => {
  return wiseTrackAssets.value.filter(asset => 
    asset.status === 'Removed'
  );
});

// Fetch employees based on search query
const fetchEmployees = async (searchQuery: string) => {
  // Clear employee if search query is empty
  if (!searchQuery) {
    form.value.employee = null;
    form.value.legalIds = [];
    form.value.comment = '';
    employees.value = [];
    showAssetsSection.value=false
    wiseTrackAssets.value=[]
    return;
  }
  
  // Skip API call if the search query matches the selected employee's display name
  if (form?.value.employee?.displayName === searchQuery) {
    return;
  }
  
  try {
    loadingEmps.value = true;
    const response = await CanonAuth.getUsersList(searchQuery);
    employees.value = response.map((user: any) => ({
      id: user.id,
      displayName: user.displayName,
      mail: user.mail,
      mailNickname: user.mailNickname,
      jobTitle: user.jobTitle,
      mobilePhone: user.mobilePhone,
      department: user.department,
      employeeId: user.employeeId,
      office: user.office,
      companyName:user.companyName,
      approverMicrosoftAccountId: user.manager?.id

    }))
    // .filter(user => user.mail?.toLowerCase().endsWith('@canada.canon.com'));; // To filter only canda canon employees
  } catch (error) {
    console.error('Error fetching employees:', error);
  } finally {
    loadingEmps.value = false;
  }
};

// Add a new method to fetch employee by Microsoft ID (for edit mode)
const fetchEmployeeById = async (microsoftId: string) => {
  if (!microsoftId) return;
  
  try {
    loadingEmps.value = true;
    // Use the API method to get user details by Microsoft ID
    const response = await getUserDetailsByMicrosoftId(microsoftId);
    const userData = response.data;
    
    if (!userData) {
      throw new Error("Error fetching user details");
    }
    
    // Create employee object from user data
    const employee: Employee = {
      id: userData.id,
      displayName: userData.displayName,
      mail: userData.mail,
      mailNickname: userData.mailNickname,
      jobTitle: userData.jobTitle,
      mobilePhone: userData.mobilePhone,
      department: userData.department,
      employeeId: userData.employeeId,
      office: userData.office,
      companyName:userData.companyName,
      approverMicrosoftAccountId: userData.manager?.id
    };
    
    // Set the employee in the form
    form.value.employee = employee;
    
  } catch (error) {
    console.error('Error fetching employee by ID:', error);
  } finally {
    loadingEmps.value = false;
  }
};

const fetchLegalAgreementsList = async () => {
  try {
    appStore.startLoader( 'fetchingAgrments', t( 'app.auth.loader.fetchingLegalDocs' ) );

    const response = await fetchLegalAgreements();
    legalIds.value = response.data.filter(agreement=>agreement.isActive=='Y');;
  } catch (error) {
    console.error('Error fetching legal agreements:', error);
  }
  finally{
    appStore.stopLoader('fetchingAgrments');

  }
};
onMounted(async () => {
  fetchLegalAgreementsList();
  fetchAccessoriesList();

  if (props.mode === 'edit' && agreementDetails) {
    // Populate form with agreement data
    await initializeFormWithAgreementData();
  }
});

const initializeFormWithAgreementData = async () => {
  if (!props.agreementData) return;
  
  // Set device agreement ID
  deviceAgreementId.value = props.agreementData.deviceAgreementId || null;
  agreementDetails.value=props.agreementData;
  // Set employee data if available
  if (agreementDetails.value.microsoftAccountId) {
    // Fetch the full employee details using the Microsoft ID
    fetchEmployeeById(agreementDetails.value.microsoftAccountId);
  }
  
  // Set legal IDs
  form.value.legalIds = agreementDetails.value.daLegalList?.map(el => el.legalId) || [];
  
  // Set comment
  form.value.comment = agreementDetails.value.comment || '';
  
  // Set assets
  wiseTrackAssets.value = agreementDetails.value.daAssetList || [];
  
  // Set accessories - wait for accessories to load first
  const slectedIDs = agreementDetails.value.daAccessoryList?.filter(el=>el.status=='Active').map(el => el.accessoryId) || [];
  userAccessories.value = agreementDetails.value.daAccessoryList?.filter(el=>el.status=='Active' ) || [];
  // Ensure accessories are loaded before filtering
  if (accessories.value.length === 0) {
    await fetchAccessoriesList();
  }

  const activeIds = accessories.value
    .filter(el => el.accessoryId && slectedIDs.includes(el.accessoryId))
    .map(el => el.accessoryId!)
    .filter(id => id !== undefined) || [];

    
  // const nonActiveIds =slectedIDs.filter(id => !activeIds.includes(id));
// removedAccessoryIds.value.push(...nonActiveIds);
selectedAccessory.value = slectedIDs||[];
  
  // If there are assets or accessories, show the assets section
  if (wiseTrackAssets.value.length > 0 || selectedAccessory.value.length > 0) {
    showAssetsSection.value = true;
  }
};

const debouncedFetchEmployees = debounce(fetchEmployees, 400);



const employeeDetails = computed(() => {
  const employee = form.value.employee;
  return {
    "Name": employee?.displayName,
    "Department": employee?.department,
    "ID": employee?.mailNickname,
    "E-mail": employee?.mail,
  };
});
watch(() => form.value.legalIds, (newValue) => {
  if (newValue.length > 0) {
    legalAgreementError.value = '';
  }
}, { deep: true });

const saveAsDraft= ()=>{
  submitForm(DeviceAgreementAction.ADD_ASSET_OR_ACCESSORY,'Draft')
}
const saveAsSubmit= ()=>{
  submitForm(DeviceAgreementAction.SUBMIT,'Submit')
}
// Handle legal agreement mapping
const handleLegalAgreementMapping = async () => {
  if (!deviceAgreementId.value) return;
  
  // Find legal IDs that are in form.value.legalIds but not in agreementDetails.value.daLegalList
  const newLegalIds = form.value.legalIds.filter(legalId => 
    !agreementDetails.value?.daLegalList?.some(legal => legal.legalId === legalId)
  );
  
  // If there are new legal IDs, create a payload and call the API
  if (newLegalIds.length > 0) {
    const legalMappingPayload = {
      deviceAgreementId: deviceAgreementId.value,
      legalIds: newLegalIds,
      action: DeviceAgreementAction.MAP_LEGAL,
      comment: form.value.comment
    };
    
    await saveExistingAgreement(legalMappingPayload);
  }
};

// Handle legal agreement removal
const handleLegalAgreementRemoval = async () => {
  if (!agreementDetails.value?.daLegalList || !deviceAgreementId.value) return;
  
  // Find legal agreements that were in agreementDetails.value.daLegalList but are no longer in form.value.legalIds
  const removedLegalMappings = agreementDetails.value.daLegalList.filter(legal => 
    !form.value.legalIds.includes(legal.legalId)
  );
  
  // Extract the daLegalMappingId values
  const removeLegalDaMappingIds = removedLegalMappings
    .map(legal => legal.daLegalMappingId)
    .filter((id): id is number => id !== undefined);
  
  // If there are legal agreements to remove, create a payload and call the API
  if (removeLegalDaMappingIds.length > 0) {
    const legalRemovalPayload = {
      deviceAgreementId: deviceAgreementId.value,
      removeLegalDaMappingIds,
      action: DeviceAgreementAction.REMOVE_LEGAL,
      comment: form.value.comment
    };
    
    await saveExistingAgreement(legalRemovalPayload);
  }
};

// Form submission handler
const submitForm = async (status:DeviceAgreementAction, saveMode: 'Draft' | 'Submit' = 'Draft') => {
  // Reset error
  legalAgreementError.value = '';
  
  if (!form.value.legalIds.length) {
  legalAgreementError.value = t('agreement.form.selectAtLeastOneLegalAgreement');
    return;
  }
  
  const oId=form.value.employee?.id||''
 if(oId==''){
    snackbar.show({
          text: t( `agreement.form.selectUser` ),
                    timeout: 5000,
                    color: 'erroe',
                    close: true,
                    icon: 'verified_user'
        });
  return;
 }

  const {comment,hasEmail,sendNotification,legalIds}=form.value;
  
  // Extract IDs from wiseTrackAssets and selectedAccessory
  const wiseTrackAssetIds = wiseTrackAssets.value
    .map(asset => asset.id)
    .filter((id): id is number => id !== undefined && !removedAssetIds.value.includes(id));
  const accessoryIds = selectedAccessory.value
  
  try {
    appStore.startLoader('savingAgreement', t('app.auth.loader.savingAgreement'));
    
    if(!deviceAgreementId.value){
      const payload = {
        microsoftAccountId: oId,
        comment,
        hasEmail,
        sendNotification,
        legalIds,
        status:status==DeviceAgreementAction.ADD_ASSET_OR_ACCESSORY?0:1,
        wiseTrackAssetIds,
        accessoryIds
      };
      
      const result=await saveUserAgreement(payload);
      // if(payload.action==DeviceAgreementAction.ADD_ASSET_OR_ACCESSORY){
      deviceAgreementId.value=result.data.deviceAgreementId;
      router.push(`edit/${ deviceAgreementId.value!}`); // Change to appropriate route

//  }
      snackbar.show({
                   text: t(`app.messages.agreementSaved` ),
                    timeout: 5000,
                    color: 'success',
                    close: true,
                    icon: 'verified_user'
        });
    }
    else{

      
      const payload = {
        deviceAgreementId: deviceAgreementId.value,
        comment,
        hasEmail,
        sendNotification,
        action:status,
      };
        const removePayload = {
          deviceAgreementId: deviceAgreementId.value,
          ...(removedAssetIds.value.length > 0 && { removeAssetMappingIds: removedAssetIds.value }),
          ...(removedAccessoryIds.value.length > 0 && { removeAccessoryMappingIds: removedAccessoryIds.value })
        }
        // Only calling if there are items to remove
        if (removedAssetIds.value.length > 0 || removedAccessoryIds.value.length > 0) {
          await saveExistingAgreement({...removePayload, comment, action: DeviceAgreementAction.REMOVE_ASSET_OR_ACCESSORY});
        }
              // Handle legal agreement mapping and removal first
      await handleLegalAgreementMapping();
      await handleLegalAgreementRemoval();
        // Handle assets with changed status
        const changedStatusPayload = {
          deviceAgreementId: deviceAgreementId.value,
          wiseTrackAssetIds: [
            ...(assetsStatusChanged.value.length > 0 ? assetsStatusChanged.value : []),
            ...wiseTrackAssets.value
              .filter(asset => asset.status === 'Active' && !asset.daAssetMappingId && (asset.wiseTrackId||asset.id))
              .map(asset => asset.wiseTrackId||asset.id)
          ].filter(id => id !== undefined),
          accessoryIds: selectedAccessory.value
            .filter(id => {
              // Find if this accessory exists in the daAccessoryList
              const existingAccessory = agreementDetails.value?.daAccessoryList?.find(acc => 
                acc.status === 'Active' && acc.accessoryId === id
              );
              // Only include if it doesn't exist or doesn't have a mapping ID
              return !existingAccessory || !existingAccessory.daAccessoryMappingId;
            })
        }
        
        // Only calling if there are items with changed status
        if (changedStatusPayload.wiseTrackAssetIds.length > 0||changedStatusPayload.accessoryIds.length > 0) {
          await saveExistingAgreement({...changedStatusPayload, action: DeviceAgreementAction.ADD_ASSET_OR_ACCESSORY});
        }
        
        // Only calling if the action is different from ADD_ASSET_OR_ACCESSORY
        // or if we haven't called it with that action yet
        if (status !== DeviceAgreementAction.ADD_ASSET_OR_ACCESSORY || 
            (changedStatusPayload.wiseTrackAssetIds.length === 0 && 
             changedStatusPayload.accessoryIds.length === 0)) {
         const result= await saveExistingAgreement(payload);
             if(status===DeviceAgreementAction.SUBMIT){
          router.push({ name: 'daList'}); // Change to appropriate route
        }
        else{
          // Refresh the event logs after saving as draft
          if (eventLogRef.value && deviceAgreementId.value) {
            eventLogRef.value.fetchEventLogs();
          }
        }
  agreementDetails.value = result.data;
  deviceAgreementId.value = result.data.deviceAgreementId;
  
  // Update form values to keep data in sync
  if (agreementDetails.value) {
    // Update legal IDs
    form.value.legalIds = agreementDetails.value.daLegalList?.map(el => el.legalId) || [];
    
    // Update comment
    form.value.comment = agreementDetails.value.comment || '';
    
    // Update email settings if they exist in the response
    if (agreementDetails.value.hasEmail !== undefined) {
      form.value.hasEmail = agreementDetails.value.hasEmail === 'Y' ? 'Y' : 'N';
    }
    
    if (agreementDetails.value.sendNotification !== undefined) {
      form.value.sendNotification = agreementDetails.value.sendNotification === 'Y' ? 'Y' : 'N';
    }
    
 
      // Update assets and accessories
      wiseTrackAssets.value = agreementDetails.value.daAssetList || [];
      selectedAccessory.value = agreementDetails.value.daAccessoryList?.filter(el => (el.status === 'Active')).map(el => el.accessoryId) || [];
      
      // Reset tracking arrays since we've just updated from the server
      removedAssetIds.value = [];
      removedAccessoryIds.value = [];
      assetsStatusChanged.value = [];
    
  }
        }

        snackbar.show({
          text: t(`app.messages.agreementSaved` ),
                    timeout: 5000,
                    color: 'success',
                    close: true,
                    icon: 'verified_user'
        });
    
    }
  } catch (error) {
    console.error('Error saving agreement:', error);
    snackbar.show({
      text: t(`Failed to save agreement: ${error instanceof Error ? error.message : 'Unknown error'}`),
      timeout: 5000,
      color: 'error',
      close: true,
      icon: 'error'
    });
  } finally {
    appStore.stopLoader('savingAgreement');
  }
};
const saveExistingAgreement = async (payload: DeviceAgreementAssetsPayload) => {
  const result = await updateAgreement(payload);
  return result
 
}

const nextStep = () => {
  showAssetsSection.value = true;
  getWiseTrackData()
};
const getWiseTrackData = async() => {
  try {
    appStore.startLoader('fetchingAssets', t('app.auth.loader.fetchingAssets'));
    const response = await gerUserWiseTrackAssets(form.value.employee?.mailNickname!);
    
    // If we already have assets from agreementDetails.value.daAssetList
    if (agreementDetails.value?.daAssetList && agreementDetails.value.daAssetList.length > 0) {
      // Map through the API response and preserve status for matching assets
      wiseTrackAssets.value = response.data.map((newAsset: WiseTrackAsset) => {
        // Try to find matching asset in existing data
        const existingAsset = agreementDetails.value!.daAssetList?.find(
          asset => asset.wiseTrackId === newAsset.wiseTrackId
        );
        
        // If found, preserve its status and mapping ID
        if (existingAsset) {
          return {
            ...newAsset,
            status: existingAsset.status,
            daAssetMappingId: existingAsset.daAssetMappingId
          };
        }
        
        // Otherwise return the new asset as is
        return newAsset;
      });
    } else {
      // If no existing assets, just use the API response
      wiseTrackAssets.value = response.data || [];
    }
  } catch (error) {
    console.error('Error fetching WiseTrack assets:', error);
    snackbar.show({
      text: t('Failed to fetch assets'),
      timeout: 3000,
      color: 'error',
      close: true,
      icon: 'error'
    });
  } finally {
    appStore.stopLoader('fetchingAssets');
  }
};

const removeAsset = (index: number) => {
  // Store the index of the asset to be removed
  assetIndexInConfirm.value = index;
  actionType.value = 'remove';
  // Open the confirmation dialog
  const dialogStore = useDialogStore();
  dialogStore.openConfirmlDialog();
};

const initiateRemoveAsset = (asset: WiseTrackAsset) => {
  // Store the index of the asset to be removed
      const id=asset.id||asset.wiseTrackId
if(!id)
return

  assetIndexInConfirm.value = id;
  actionType.value = 'remove';
  
  // Open the confirmation dialog
  const dialogStore = useDialogStore();
  dialogStore.setConfirmCallback(() => handleConfirmRemove());
  dialogStore.setDialogTitle(t('agreement.dialog.removeAsset'));
  dialogStore.setDialogMessage(t('agreement.dialog.confirmRemove'));
  dialogStore.setConfirmButtonText(t('agreement.dialog.remove'));
  dialogStore.setCancelButtonText(t('agreement.dialog.cancel'));
  dialogStore.openConfirmlDialog();
};



const handleConfirmRemove = () => {
  if (assetIndexInConfirm.value !== undefined) {
    if (actionType.value === 'remove') {
      // Get the asset to be removed
      const asset = wiseTrackAssets.value.find(asset=>asset.id==assetIndexInConfirm.value||asset.wiseTrackId==assetIndexInConfirm.value);
      

      if(!asset)
      return
      // Add the asset ID to the removedAssetIds array if it's not already there
      if (asset.daAssetMappingId !== undefined && !removedAssetIds.value.includes(asset.daAssetMappingId)) {
        removedAssetIds.value.push(asset.daAssetMappingId);
      }
      
      // Remove from assetsStatusChanged if it was there (in case it was previously added)
      const id=asset.id||asset.wiseTrackId
      if (id) {
        const idIndex = assetsStatusChanged.value.indexOf(id);
        if (idIndex !== -1) {
          assetsStatusChanged.value.splice(idIndex, 1);
        }
      }
                // Mark the asset as "Removed" instead of deleting it
      asset.status = 'Removed';
    } 

    
    // Reset the assetIndexInConfirm and actionType
    assetIndexInConfirm.value = null;
    actionType.value = null;

  }
  // Close the dialog
  const dialogStore = useDialogStore();
  dialogStore.closeConfirmDialog();
};

const handleCancelRemove = () => {
  // Reset the assetIndexInConfirm and actionType
  assetIndexInConfirm.value = null;
  actionType.value = null;
  // Close the dialog
  const dialogStore = useDialogStore();
  dialogStore.closeConfirmDialog();
};



const fetchAccessoriesList = async () => {
  const response = await fetchAccessories();
  accessories.value = response.data.filter((accessory:Accessory)=>accessory.isActive=='Y');
};

const fetchAgreementDetails = async (employee:Employee) => {
  if(!employee){
    return
  }
  try {
    appStore.startLoader( 'checkingUserDetails', t( 'app.auth.loader.checkingUserDetails' ) );
    const payload={
	oid:employee.id,
	name :employee.displayName,
	email:employee.mail||'',
	preferred_username:employee.displayName,
	employeeId:employee.mailNickname,
  approverMicrosoftAccountId: employee.approverMicrosoftAccountId
}
    const response = await checkUserDetails(payload);
    deviceAgreementId.value = response.data?.deviceAgreementId;
    userName.value = response.data?.userName || 'User';
    if (deviceAgreementId.value && deviceAgreementId.value > 0) {
      // Reset dialog state before showing the dialog to ensure fresh state
      resetDialogState();
      dialogVisible.value = true;
    }
  } catch (error) {
    console.error('Error fetching agreement details:', error);
    // agreementDetails.value = null;
  }
  finally{
    appStore.stopLoader('checkingUserDetails');

  }
};
const getLabel = (agreement: LegalAgreement) =>
appStore.currentLanguage=== 'en-US'? agreement.nameEnglish : agreement.nameFrench;

// Function to reset dialog state for fresh display
const resetDialogState = () => {
  const dialogStore = useDialogStore();

  // Reset DialogStore state
  dialogStore.closeConfirmDialog();
  dialogStore.setDialogTitle('');
  dialogStore.setDialogMessage('');
  dialogStore.setConfirmButtonText('');
  dialogStore.setCancelButtonText('');
  dialogStore.setConfirmCallback(() => {});

  // Reset local dialog state
  actionType.value = null;
  assetIndexInConfirm.value = null;
};

// Action Handlers
const onConfirm = () => {
  // form.value.comment=agreementDetails.value?.comment||'';
  // form.value.legalIds=agreementDetails.value?.daLegalList?.map(el=>el.legalId)||[];
  // selectedAccessory.value=agreementDetails.value?.daAccessoryList?.map(el=>el.accessoryId)||[];
  // wiseTrackAssets.value=agreementDetails.value?.daAssetList||[];
  // console.log(form.value)
  router.push(`edit/${ deviceAgreementId.value!}`); // Change to appropriate route


  console.log('User chose to view/edit the agreement');
};

const onCancel = () => {
  console.log('User chose not to view the agreement');
  // Reset dialog state when canceling
  resetDialogState();
};

// Add these refs for barcode input
const barcodeInput = ref('');
const loadingBarcode = ref(false);
const pendingAssetData = ref<any>(null);

// Add function to fetch asset by barcode
const addAssetByBarcode = async () => {
  if (!barcodeInput.value) return;
  
  try {
    loadingBarcode.value = true;
    const exists = activeAssets.value.some(a => a.barcode === barcodeInput.value);
    if(exists){
    snackbar.show({
          // text: t(`app.messages.assetsExits `,{barcode:barcodeInput.value} ),
          text: t(`app.messages.assetExists`,{barcode:barcodeInput.value} ),

                    timeout: 5000,
                    color: 'warning',
                    close: true,
                    icon: 'verified_user'
        });
      return
    }
    const response = await getWiseTrackAssetByBarcode(barcodeInput.value);
    
    if (response.data) {
      // Check if asset already exists
      const exists = activeAssets.value.some(a => a.barcode === response.data[0].barcode);
      const existsInRemovedIndex = wiseTrackAssets.value.findIndex(a => a.barcode === response.data[0].barcode && a.status=="Removed");

      if(existsInRemovedIndex>=0){
        wiseTrackAssets.value[existsInRemovedIndex].status='Active'
        // wiseTrackAssets.value.splice(existsInRemovedIndex,1)
        // Clear the input and show success message
        barcodeInput.value = '';
        snackbar.show({
          text: t( `app.messages.restoreAssetSuccess`, {barcode:response.data[0].barcode}),
          timeout: 5000,
          color: 'success',
          close: true,
          icon: 'verified_user'
        });
        return;
      }

      if (!exists) {
        // Store the pending asset data and show confirmation dialog
        pendingAssetData.value = response.data[0];

        const dialogStore = useDialogStore();
        const employeeName = response.data[0].employee || 'Unknown Employee';
        const currentUserName = form.value.employee?.displayName || 'Current User';

        dialogStore.setConfirmCallback(() => confirmAddAsset());
        dialogStore.setDialogTitle('Confirm Asset Assignment');
        dialogStore.setDialogMessage(
          t(`agreement.dialog.confirmAssetAssignment`, { employeeName, barcodeInput: barcodeInput.value, currentUserName })
        );
        dialogStore.setConfirmButtonText('Assign Asset');
        dialogStore.setCancelButtonText('Cancel');
        dialogStore.openConfirmlDialog();
      } else {
        snackbar.show({
          text: t(  `Asset with barcode ${barcodeInput.value} already exists` ),
          timeout: 5000,
          color: 'warning',
          close: true,
          icon: 'verified_user'
        });
        // Clear the input
        barcodeInput.value = '';
      }
    }
  } catch (error) {
    console.error('Error fetching asset by barcode:', error);

    snackbar.show({
          text: t(  `Failed to add asset: ${error instanceof Error ? error.message : 'Unknown error'}` ),
                    timeout: 5000,
                    color: 'error',
                    close: true,
                    icon: 'verified_user'
        });
  } finally {
    loadingBarcode.value = false;
  }
};

// Function to confirm and add the asset after dialog confirmation
const confirmAddAsset = () => {
  if (pendingAssetData.value) {
    // Add the asset to wiseTrackAssets
    wiseTrackAssets.value.push(pendingAssetData.value);

    // Show success message
    snackbar.show({
      text: t( `Asset with barcode ${pendingAssetData.value.barcode} added successfully` ),
      timeout: 5000,
      color: 'success',
      close: true,
      icon: 'verified_user'
    });

    // Clear the input and pending data
    barcodeInput.value = '';
    pendingAssetData.value = null;
  }
};

// Add a ref for tracking removed accessories
const removedAccessoryIds = ref<number[]>([]);



// New functions for accessories management
const addAccessory = (accessoryId: number | null) => {
  if (accessoryId && !selectedAccessory.value.includes(accessoryId)) {
    selectedAccessory.value.push(accessoryId);
    const selectedAcc = accessories.value.find(accessory => accessory.accessoryId == accessoryId);
    if (selectedAcc) {
      userAccessories.value.push(selectedAcc);
    }
    // Clear the selection dropdown
    accessoryToAdd.value = null;
  }
};

const confirmRemoveAccessory = (accessory: Accessory) => {
  accessoryToRemove.value = accessory;


if(!accessoryToRemove.value)
return

  
  // Open the confirmation dialog
  const dialogStore = useDialogStore();
  dialogStore.setConfirmCallback(() => removeAccessory());
  dialogStore.setDialogTitle(t('agreement.dialog.removeAccessory'));
  dialogStore.setDialogMessage(t('agreement.dialog.confirmRemoveAccessory'));
  dialogStore.setConfirmButtonText(t('agreement.dialog.remove'));
  dialogStore.setCancelButtonText(t('agreement.dialog.cancel'));
  dialogStore.openConfirmlDialog();


};

const removeAccessory = () => {
  const accessoryIdToRemove = accessoryToRemove.value?.accessoryId;

  if (accessoryIdToRemove) {
    // Remove accessory ID from selectedAccessory
    selectedAccessory.value = selectedAccessory.value.filter(
      id => id !== accessoryIdToRemove
    );

    const daList = agreementDetails.value?.daAccessoryList;
    if (daList) {
      const existingAccessory = daList.find(
        acc => acc.accessoryId === accessoryIdToRemove
      );

      if (
        existingAccessory &&
        !removedAccessoryIds.value.includes(existingAccessory.daAccessoryMappingId)
      ) {
        // Track the removed mapping ID
        removedAccessoryIds.value.push(existingAccessory.daAccessoryMappingId);

        // Also remove from userAccessories based on daAccessoryMappingId
        userAccessories.value = userAccessories.value.filter(
          acc => acc.daAccessoryMappingId !== existingAccessory.daAccessoryMappingId
        );
      }
    }
  }
};



// Define action buttons with visibility conditions
const actionButtons = computed(() => [
  {
    text: 'View Agreement',
    color: 'primary',
    visible: true, // Always visible
    action: () => viewAgreement()
  },
  {
    text: 'Terminate',
    color: 'error',
    visible: agreementDetails.value?.status !== 3, // Hide if already terminated
    action: () => terminateAgreement()
  },
  {
    text: 'Get Link',
    color: 'info',
    visible: true, // Always visible
    action: () => getAgreementLink()
  },
  {
    text: 'Teams Chat',
    color: 'success',
    icon: 'mdi-microsoft-teams',
    visible: agreementDetails.value?.status === 1, // Only show for pending agreements
    action: () => getTeamsLink()
  },
  {
    text: 'Send Reminder',
    color: 'warning',
    visible: agreementDetails.value?.status === 1, // Only show for pending agreements
    action: () => sendReminder()
  },
  {
    text: 'Reactivate',
    color: 'success',
    visible: agreementDetails.value?.status === 3, // Only showing for terminated agreements
    action: () => reactivateAgreement()
  }
]);

// Action handler functions
const viewAgreement = () => {
  console.log('View agreement clicked');
  router.push(`../view/${ deviceAgreementId.value!}`);//routing to the view page

};
// Action handler functions
const getTeamsLink = () => {
  console.log('Teams link clicked');
  
  // Get the tenant ID from environment variables
  const tenantId = import.meta.env.VITE_APP_AUTH_TENANT_ID;
  
  // Get the user's email from the form data
  const userEmail = form.value.employee?.mail || '';
  
  // Create the base URL for the agreement view
  const baseUrl = window.location.origin + import.meta.env.BASE_URL;
  
  // Create the view URL for the agreement
  const viewUrl = `${baseUrl}/view/${deviceAgreementId.value}`;
  
  // Create the message with the agreement link
  const message = `Please review and acknowledge your device agreement: <a target='blank' href='${viewUrl}'>Device Agreement</a>`;
  
  // Create the Teams deep link
  const teamsUrl = `https://teams.microsoft.com/l/chat/0/0?tenantId=${tenantId}&users=${userEmail}&message=${encodeURIComponent(message)}`;
  
  // Open the Teams link in a new tab
  window.open(teamsUrl, '_blank');
}; // Generic method to handle agreement actions
const handleAgreementAction = async (action: DeviceAgreementAction, comment: string = '') => {
  const actionLabels = {
    [DeviceAgreementAction.REMIND_USER]: 'reminder',
    [DeviceAgreementAction.TERMINATE]: 'termination',
    [DeviceAgreementAction.REACTIVATE]: 'reactivation'
  };

  const actionLabel = actionLabels[action as keyof typeof actionLabels] || 'action';

  // Translation keys for success messages
  const successMessages = {
    [DeviceAgreementAction.REMIND_USER]: 'agreement.form.snacks.reminderProcessed',
    [DeviceAgreementAction.TERMINATE]: 'agreement.form.snacks.terminationProcessed',
    [DeviceAgreementAction.REACTIVATE]: 'agreement.form.snacks.reactivationProcessed'
  };

  try {
    appStore.startLoader(`processing${actionLabel}`, t(`Processing ${actionLabel}...`));

    const payload = {
      deviceAgreementId: Number(deviceAgreementId.value),
      action: action,
      comment: comment
    };

    const response = await saveExistingAgreement(payload);

    snackbar.show({
      text: t(successMessages[action as keyof typeof successMessages] || 'agreement.form.snacks.reminderProcessed'),
      timeout: 3000,
      color: 'success',
      close: true,
      icon: action === DeviceAgreementAction.REMIND_USER ? 'mail' : 'check_circle'
    });
    
    // Refresh data if needed
    if (action === DeviceAgreementAction.TERMINATE || action === DeviceAgreementAction.REACTIVATE) {
      // Optionally refresh the agreement data
      // await fetchAgreementData();

      // Reset dialog state after reactivation/termination to ensure clean state for next dialog
      resetDialogState();
    }

    return response;
  } catch (error) {
    console.error(`Error processing ${actionLabel}:`, error);

    // Translation keys for error messages
    const errorMessages = {
      [DeviceAgreementAction.REMIND_USER]: 'agreement.form.snacks.reminderFailed',
      [DeviceAgreementAction.TERMINATE]: 'agreement.form.snacks.terminationFailed',
      [DeviceAgreementAction.REACTIVATE]: 'agreement.form.snacks.reactivationFailed'
    };

    snackbar.show({
      text: t(errorMessages[action as keyof typeof errorMessages] || 'agreement.form.snacks.reminderFailed'),
      timeout: 3000,
      color: 'error',
      close: true,
      icon: 'error'
    });

    throw error;
  } finally {
    appStore.stopLoader(`processing${actionLabel}`);
  }
};

const terminateAgreement = () => {
  console.log('Terminate agreement clicked');
  
  // Set up confirmation dialog
  const dialogStore = useDialogStore();
  dialogStore.setConfirmCallback(() => {
    return handleAgreementAction(DeviceAgreementAction.TERMINATE);
  });
  
  // Set dialog title and message
  dialogStore.setDialogTitle(t('agreement.dialog.terminateTitle'));
  dialogStore.setDialogMessage(t('agreement.dialog.terminateConfirm'));
  dialogStore.setConfirmButtonText(t('agreement.dialog.terminate'));
  dialogStore.setCancelButtonText(t('agreement.dialog.cancel'));
  
  // Open the confirmation dialog
  dialogStore.openConfirmlDialog();
};

const getAgreementLink = () => {
  console.log('Get link clicked');
  // Create the URL for the view page
  const viewUrl = window.location.origin + router.resolve({
    path: `../view/${deviceAgreementId.value!}`
  }).href;
  
  // Copy to clipboard
  navigator.clipboard.writeText(viewUrl)
    .then(() => {
      // Show success snackbar
      snackbar.show({
        text: t('agreement.form.snacks.linkcopied'),
        timeout: 3000,
        color: 'success',
        close: true,
        icon: 'content_copy'
      });
    })
    .catch(err => {
      console.error('Failed to copy link:', err);
      snackbar.show({
        text: t('agreement.form.snacks.failedtocopy'),
        timeout: 3000,
        color: 'error',
        close: true,
        icon: 'error'
      });
    });
};
const dialogVisibleComputed = computed(() => {
  return dialogVisible.value || useDialogStore().isConfirmDialogOpen;
});

const sendReminder = () => {
  console.log('Send reminder clicked');
  
  // Set up confirmation dialog
  const dialogStore = useDialogStore();
  dialogStore.setConfirmCallback(() => {
    return handleAgreementAction(DeviceAgreementAction.REMIND_USER);
  });
  
  // Set dialog title and message
  dialogStore.setDialogTitle(t('agreement.dialog.reminderTitle'));
  dialogStore.setDialogMessage(t('agreement.dialog.reminderConfirm'));
  dialogStore.setConfirmButtonText(t('agreement.dialog.send'));
  dialogStore.setCancelButtonText(t('agreement.dialog.cancel'));
  
  // Open the confirmation dialog
  dialogStore.openConfirmlDialog();
};

const reactivateAgreement = () => {
  console.log('Reactivate agreement clicked');
  
  // Set up confirmation dialog
  const dialogStore = useDialogStore();
  dialogStore.setConfirmCallback(() => {
    return handleAgreementAction(DeviceAgreementAction.REACTIVATE);
  });
  
  // Set dialog title and message
  dialogStore.setDialogTitle(t('agreement.dialog.reactivateTitle'));
  dialogStore.setDialogMessage(t('agreement.dialog.reactivateConfirm'));
  dialogStore.setConfirmButtonText(t('agreement.dialog.reactivate'));
  dialogStore.setCancelButtonText(t('agreement.dialog.cancel'));
  
  // Open the confirmation dialog
  dialogStore.openConfirmlDialog();
};

// Helper methods for the unified confirmation dialog
const getDialogTitle = () => {
  if (props.mode === 'add' && dialogVisible.value) {
    return t('agreement.dialog.notice');
  }
  
  if (actionType.value === 'remove') {
    return t('agreement.dialog.removeAsset');
  } else if (actionType.value === 'restore') {
    return t('agreement.dialog.restoreAsset');
  }
  
  return useDialogStore().dialogTitle;
};

const getDialogMessage = () => {
  if (props.mode === 'add' && dialogVisible.value) {
    return t('agreement.dialog.existingAgreement', { name: userName.value });
  }
  
  if (actionType.value === 'remove') {
    return t('agreement.dialog.confirmRemove');
  } else if (actionType.value === 'restore') {
    return t('agreement.dialog.confirmRestore');
  }
  
  return useDialogStore().dialogMessage;
};

const getConfirmButtonText = () => {
  if (actionType.value === 'remove') {
    return t('agreement.dialog.remove');
  } else if (actionType.value === 'restore') {
    return t('agreement.dialog.restore');
  }
  
  return useDialogStore().confirmButtonText || t('agreement.dialog.yes');
};

const getCancelButtonText = () => {
  return useDialogStore().cancelButtonText || t('agreement.dialog.no');
};

const handleDialogConfirm = () => {
  if (props.mode === 'add' && dialogVisible.value) {
    onConfirm();
    dialogVisible.value = false;
    resetDialogState(); // Reset state after confirming
  } else if (actionType.value === 'remove' || actionType.value === 'restore') {
    handleConfirmRemove();
  } else {
    useDialogStore().executeConfirmCallback();
    useDialogStore().closeConfirmDialog();
    resetDialogState(); // Reset state after any dialog store action
  }
};

const handleDialogCancel = () => {
  if (props.mode === 'add' && dialogVisible.value) {
    onCancel();
    dialogVisible.value = false;
    resetDialogState(); // Reset state after canceling
  } else if (actionType.value === 'remove' || actionType.value === 'restore') {
    handleCancelRemove();
  } else {
    useDialogStore().closeConfirmDialog();
    resetDialogState(); // Reset state after any dialog store action
  }
};
</script>

<template>
  
  <v-container class="d-flex flex-column align-center">
    <v-card class="pa-4 elevation-2 mb-4" style="max-width: 600px; width: 100%;" v-if="deviceAgreementId">
      <v-card-title class="text-green text-h6 justify-center text-center">
        <StatusBadge :status="agreementDetails?.status || 0" />
      </v-card-title>
  
      <v-card-text class="text-body-2">
        <div class="d-flex justify-space-between">
          <span><strong>{{ t('agreement.details.lastModifiedBy') }}</strong></span>
          <span>{{ agreementDetails?.lastModifiedByAdminName || t('agreement.details.na') }}</span>
        </div>
        <div class="d-flex justify-space-between">
          <span><strong>{{ t('agreement.details.lastModified') }}</strong></span>
          <span>{{ agreementDetails?.lastModifiedByAdminDate ? new Date(agreementDetails.lastModifiedByAdminDate).toLocaleString() : t('agreement.details.na') }}</span>
        </div>
        <div class="d-flex justify-space-between">
          <span><strong>{{ t('agreement.details.employeeNotifyDate') }}</strong></span>
          <span>{{ agreementDetails?.employeeNotifyDate ? new Date(agreementDetails.employeeNotifyDate).toLocaleString() : t('agreement.details.na') }}</span>
        </div>
        <div class="d-flex justify-space-between">
          <span><strong>{{ t('agreement.details.employeeAcceptanceDate') }}</strong></span>
          <span>{{ agreementDetails?.employeeAcceptDate ? new Date(agreementDetails.employeeAcceptDate).toLocaleString() : t('agreement.details.na') }}</span>
        </div>
        <div class="d-flex justify-space-between">
          <span><strong>{{ t('agreement.details.employeeAcceptanceName') }}</strong></span>
          <span>{{ agreementDetails?.employeeAcceptName || t('agreement.details.na') }}</span>
        </div>
  
        <v-divider class="my-4"></v-divider>
  
        <div class="text-center text-green text-subtitle-1 font-weight-medium mb-2">
          {{ t('agreement.details.currentLegalVersions') }}
        </div>

        <div v-for="(legal, index) in agreementDetails?.daLegalList?.filter((item, idx, arr) => 
          arr.findIndex(t => t.legalId === item.legalId) === idx) || []" 
          :key="index" 
          class="d-flex justify-space-between align-center mb-4">
          <span>{{ getLabel(legal) }}</span>
          <span><strong>{{ t('agreement.details.version', { version: legal.legalVersion }) }}</strong></span>
        </div>
  
<div class="button-group">
  <v-btn 
    v-for="(btn, index) in actionButtons" 
    :key="index"
    :color="btn.color" 
    variant="outlined" 
    size="small"
    v-show="btn.visible"
    @click="btn.action"
    class="button-item"
  >
    {{ t(`agreement.actions.${btn.text.toLowerCase().replace(/\s+/g, '')}`) }}
  </v-btn>
</div>


      </v-card-text>
    </v-card>
    <v-card class="pa-4" style="max-width: 600px; width: 100%;">
      <v-card-title class="text-h6 text-center">
        {{ props.mode === 'add' ? t('agreement.form.newAgreement') : t('agreement.form.editAgreement') }}
      </v-card-title>
  
      <v-divider></v-divider>
  
      <v-autocomplete
        v-model="form.employee"
        :label="t('agreement.form.employeeNameOrId')"
        :items="employees"
        item-title="displayName"
        item-value="id"
        return-object
        no-filter
        :filterable="false"
        @update:search="debouncedFetchEmployees"
        :loading="loadingEmps"
        @update:modelValue="form.employee && fetchAgreementDetails(form.employee)"
        clearable
        class="mt-4"
        :disabled="props.mode === 'edit'"
      >
        <!-- Loader within the field -->
        <template v-slot:append-inner>
          <v-progress-circular v-if="loadingEmps" indeterminate size="20"></v-progress-circular>
        </template>
              
        <!-- Custom display for search results -->
        <template v-slot:item="{ props, item }">
          <v-list-item v-bind="props">
            <v-list-item-title>
              {{ item?.raw?.mailNickname || '' }} | {{ item?.raw?.mail || '' }}
            </v-list-item-title>
            <v-list-item-subtitle></v-list-item-subtitle>
          </v-list-item>
        </template>
      </v-autocomplete>
    
      <!-- Employee Details -->
      <v-container class="pa-0 mt-3" v-if="employeeDetails.Name">
        <v-row v-for="(value, label) in employeeDetails" :key="label" dense>
          <v-col cols="6" class="font-weight-bold">{{ t(`agreement.form.employeeDetails.${label.toLowerCase()}`) }}</v-col>
          <v-col cols="6" class="text-right">{{ value }}</v-col>
        </v-row>
      </v-container>

      <!-- Legal Agreements -->
      <v-divider class="mx-0 px-0 mb-4"></v-divider>
      <v-card class="mt-4" flat tile>
        <v-card-title class="text-subtitle-1 font-weight-bold">{{ t('agreement.form.legal') }} *</v-card-title>
        <v-card-subtitle>{{ t('agreement.form.selectLegalAgreements') }}</v-card-subtitle>

        <v-checkbox
          v-for="agreement in legalIds"
          :key="agreement.legalId"
          v-model="form.legalIds"
          :label="getLabel(agreement)"
          :value="agreement.legalId"
          density="compact"
          :disabled="agreementDetails?.status==3"
          color="primary"
        ></v-checkbox>
        
        <div v-if="legalAgreementError" class="text-error px-4 pb-2">
          {{ legalAgreementError }}
        </div>
      </v-card>

      <!-- Comment Section -->
      <v-textarea v-model="form.comment" 
      :label="t('agreement.form.comment')"
      :maxlength="500"
      counter
      persistent-counter 
      class="mt-4" density="compact" 
      height="60px"></v-textarea>

      <v-expand-transition>
        <div v-if="showAssetsSection">
          <v-card class="mt-4">
            <v-card-title class="d-flex align-center justify-space-between">
              <span>{{ t('agreement.form.wisetrack') }}</span>
              <div class="d-flex align-center">
                <v-tooltip :text="t('agreement.form.refreshAssets')">
                  <template v-slot:activator="{ props }">
                    <v-btn 
                      icon 
                      :disabled="agreementDetails?.status==3"
                      size="small" 
                      v-bind="props" 
                      @click="getWiseTrackData"
                      class="mr-2"
                    >
                      <v-icon>refresh</v-icon>
                    </v-btn>
                  </template>
                </v-tooltip>
              </div>
            </v-card-title>
          
            <div class="px-3 py-2">
              <v-row align="center" no-gutters>
                <v-col cols="9" class="pr-2">
                  <v-text-field
                    v-model="barcodeInput" 
                    :disabled="agreementDetails?.status==3"
                    :label="t('agreement.form.enterBarcode')"
                    density="compact"
                    hide-details
                    @keyup.enter="addAssetByBarcode"
                  ></v-text-field>
                </v-col>
                <v-col cols="3">
                  <v-btn 
                    block 
                    @click="addAssetByBarcode" 
                    :loading="loadingBarcode"
                    :disabled="!barcodeInput"
                  >
                    {{ t('agreement.form.add') }}
                  </v-btn>
                </v-col>
              </v-row>
            </div>
            <v-divider></v-divider>
          
            <!-- No assets message -->
            <v-card-text v-if="!activeAssets || wiseTrackAssets.length === 0" class="text-center py-4">
              {{ t('agreement.form.noAssetsFound') }}
            </v-card-text>
          
            <!-- Active assets section -->
            <template v-else>
              
              <v-card-subtitle class="font-weight-bold">{{ t('agreement.form.activeAssets') }}</v-card-subtitle>
              <v-list density="compact">
                <v-list-item v-for="(asset, index) in activeAssets" :key="index" class="mb-2">
                  <v-card class="pa-2" width="100%" color="#f1f9ec">
                    <v-row no-gutters align="center">
                      <v-col>
                        <v-card-text>
                          <div><strong>{{ t('agreement.form.barcode') }}:</strong> {{ asset.barcode }}</div>
                          <div><strong>{{ t('agreement.form.description') }}:</strong> {{ asset.description }}</div>
                          <div><strong>{{ t('agreement.form.serial') }}:</strong> {{ asset.serial }}</div>
                        </v-card-text>
                      </v-col>
                      <v-col cols="auto" class="pr-2">
                        <v-btn icon color="error" :disabled="agreementDetails?.status==3" @click="initiateRemoveAsset(asset)" density="compact">
                          <v-icon>delete</v-icon>
                        </v-btn>
                      </v-col>
                    </v-row>
                  </v-card>
                  <v-divider></v-divider>
                </v-list-item>
                 <v-card-text v-if="activeAssets.length==0" class="text-center py-4">
              {{ t('agreement.form.noAssetsFound') }}
            </v-card-text>
              </v-list>
            
              <!-- Removed assets section (if any) -->
              <template v-if="removedAssets.length > 0">
                <v-card-subtitle class="font-weight-bold mt-3">{{ t('agreement.form.removedAssets') }}</v-card-subtitle>
                <v-list density="compact">
                  <v-list-item v-for="(asset, index) in removedAssets" :key="index" class="mb-2">
                    <v-card class="pa-2" width="100%" color="#ebcbcc">
                      <v-row no-gutters align="center">
                        <v-col>
                          <v-card-text>
                            <div><strong>{{ t('agreement.form.barcode') }}:</strong> {{ asset.barcode }}</div>
                            <div><strong>{{ t('agreement.form.description') }}:</strong> {{ asset.description }}</div>
                            <div><strong>{{ t('agreement.form.serial') }}:</strong> {{ asset.serial }}</div>
                          </v-card-text>
                        </v-col>
                        <!-- <v-col cols="auto" class="pr-2">
                          <v-btn icon color="success" @click="initiateRestoreAsset(index)" density="compact">
                            <v-icon>restore</v-icon>
                          </v-btn>
                        </v-col> -->
                      </v-row>
                    </v-card>
                    <v-divider></v-divider>
                  </v-list-item>
                </v-list>
              </template>
            </template>
            <v-divider></v-divider>

            <!-- Accessory Selection -->
            <div class="pa-3 w-100">
              <v-row align="center" no-gutters>
                <v-col cols="12" class="pr-2">
                  <!-- Single selection dropdown -->
                  <v-select
                    v-model="accessoryToAdd"
                    :items="availableAccessories"
                    :label="t('agreement.form.selectAccessory')"
                    density="compact"
                    :item-title="accessory => appStore.currentLanguage === 'en-US' ? accessory.nameEnglish : accessory.nameFrench"
                    item-value="accessoryId"
                    clearable
                    :disabled="agreementDetails?.status==3"
                    @update:model-value="addAccessory"
                  ></v-select>
                </v-col>
              </v-row>

              <!-- Selected accessories list -->
              <v-row v-if="selectedAccessoriesList.length > 0" class="mt-2">
                <v-col cols="12">
                  <v-card variant="outlined" class="pa-2">
                    <v-card-title class="text-subtitle-1 pa-2">{{t('agreement.form.selectedAccessory')}}</v-card-title>
                    <v-list density="compact">
                      <!-- {{userAccessories}} -->
                      <v-list-item
                        v-for="accessory in userAccessories"
                        :key="accessory.accessoryId"
                        class="px-2"
                      >
                      <template v-slot:prepend>
                      <span class="material-symbols-outlined">
                      package
                      </span>
                    </template>

                        <v-list-item-title>{{ appStore.currentLanguage=='en-US'?accessory.nameEnglish:accessory.nameFrench}}
</v-list-item-title>


                        <template v-slot:append>
                          <v-btn
                            icon="delete"
                            size="small"
                            color="error"
                            variant="text"
                            :disabled="agreementDetails?.status==3"
                            @click="confirmRemoveAccessory(accessory)"
                          ></v-btn>
                        </template>
                      </v-list-item>
                    </v-list>
                  </v-card>
                </v-col>
              </v-row>
            </div>
          </v-card>
        </div>
      </v-expand-transition>

      <!-- Buttons -->
      <v-row justify="space-between" class="mt-4" style="margin:0">
        <v-btn color="blue" @click="saveAsDraft" v-if="agreementDetails?.status!==3 " >{{ t('agreement.form.saveAsDraft') }}</v-btn>
        <v-btn color="primary" @click="saveAsSubmit" v-if="showAssetsSection && agreementDetails?.status!==3 ">{{ t('agreement.form.submit') }}</v-btn>
        <v-btn color="blue" @click="nextStep" v-if="!showAssetsSection && agreementDetails?.status!==3" :disabled="!form.employee || !form.legalIds.length">{{ t('agreement.form.next') }}</v-btn>
      </v-row>
      <EventLog
      ref="eventLogRef"
      v-if="deviceAgreementId"
      :device-agreement-id="deviceAgreementId"
      :expanded="eventLogExpanded"
    />
    </v-card>
  </v-container>
  <!-- Single reusable confirmation dialog -->
  <ConfirmationDialog
    v-model:isVisible="dialogVisibleComputed"
    :title="getDialogTitle()"
    :message="getDialogMessage()"
    :confirmText="getConfirmButtonText()"
    :cancelText="getCancelButtonText()"
    @confirm="handleDialogConfirm"
    @cancel="handleDialogCancel"
  />
 
</template>
