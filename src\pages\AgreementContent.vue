<template>
  <v-container>
    <v-card class="pa-5" elevation="2">
      <v-card-title class="text-h5 mb-3">{{ t('component.AgreementContent.title') }}</v-card-title>
      <v-divider class="mb-4"></v-divider>
      <div v-html="content" class="agreement-content"></div>
    </v-card>
  </v-container>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute } from 'vue-router';
import { useAppStore } from '@/stores/AppStore';

const { t } = useI18n();
const route = useRoute();
const appStore = useAppStore();
const content = ref('');

onMounted(() => {
  // Get content from URL query parameter
  const contentParam = route.query.content as string;
  if (contentParam) {
    content.value = decodeURIComponent(contentParam);
  }
  appStore.stopPageLoader();
});
</script>

<style scoped>
.agreement-content {
  line-height: 1.6;
  white-space: pre-wrap;
}
</style>