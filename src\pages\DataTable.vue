
<script setup lang="ts">
import StatusBadge from '@/components/core/StatusBadge.vue';
import { CN_Action, TableDefaultPageSize } from '@/lib/common/types';
import { useInternalUserStore } from '@/stores/InternalUserStore';
import { useTableLoader } from '@/stores/TableLoaderStore';
import { watch } from 'vue';
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';
import { useRoute } from 'vue-router';
import actionPermissions from '@/composables/auth/actionPermission';
import { useAppStore } from '@/stores/AppStore';

const tableRows = ref<any[]>()
  const headers = ref<any[]>()
const sortBy = ref([{ key: 'displayName', order: 'asc' }] as any[])
const { t,locale } = useI18n();
const tableLoading = useTableLoader();
const emit = defineEmits(['update:page', 'update:order']);

const router = useRouter();
const route = useRoute();
const itemsPerPage = ref(10);
const page = ref(0); // Initialize page as 1 instead of 0 to match Vuetify's 1-based pagination
const totalItems = ref(0);
const appStore =useAppStore()

const props = defineProps({
  data: {
    type: Object||Number,
    required: true
  }
});
watch(() => props.data, (newData) => {
  // Used to set the new data to the table
  if(newData){
    console.log(newData)
    tableRows.value = newData.tableRows;
    headers.value = newData.headers;
    totalItems.value = newData.numberOfItems || 0;
    // page.value=newData.page+1; // Add this line to update totalItems
  }
});
 // Keeps track of table state for pageSize and page
// watch(tableLoading, (newValue, oldValue) => {

// itemsPerPage.value = newValue.pageSize||2;
// page.value=newValue.page+1;
// pageCount()
// });
// watch(locale, () => {
//   headers.value = getTranslatedHeaders();
//   });





const goToRequest = (item:any) => {
  // navigate to the request view page
  console.log(route)
  router.push({ name: 'viewRequest', params: { id: item.deviceAgreementId } });
};

const editRequest = (item:any) => {
  // Navigate to edit page
  router.push({ name: 'editRequest', params: { id: item.deviceAgreementId } });
};

const viewRequest = (item:any) => {
  // For now, same as goToRequest, but can be modified later
  goToRequest(item);
};

// Returns Number of pages based on per page items and total Items
const pageCount = () => {
  return Math.ceil(totalItems.value / itemsPerPage.value)
}

// Handle all table options changes in one place
const handleTableOptions = (options: any) => {
  console.log('Table options changed:', options);
  
  // Check if page changed
  if (options.page !== page.value) {
    page.value = options.page;
    emit('update:page', { page: options.page, size: options.itemsPerPage });
  }
  
  // Check if items per page changed
  if (options.itemsPerPage !== itemsPerPage.value) {
    itemsPerPage.value = options.itemsPerPage;
    tableLoading.setPageSize(options.itemsPerPage);
    emit('update:page', { page: 1, size: options.itemsPerPage });
  }
}
const onHeaderClick = (header: any) => {
  const sortOrder = header[0];
  emit('update:order', sortOrder);

}

// Add this method to handle page size changes
const handlePageSizeChange = (newSize: number) => {
  itemsPerPage.value = newSize;
  tableLoading.setPageSize(newSize);
  page.value = 1; // Reset to first page when changing page size
  
  // Emit event to parent component to update page size
  emit('update:page', { page: 1, size: newSize });
}

// Add this method to handle pagination clicks
const onPageChange = (newPage: number) => {
  // Update the page value
  page.value = newPage;
  
  // Emit event to parent component to fetch new data
  emit('update:page', { page: newPage, size: itemsPerPage.value });
}
</script>
<template>

        <v-data-table
          :loading="tableLoading.loading"
          :headers="headers"
          :items="tableRows"
          item-key="requestNumber"
          v-model:sort-by="sortBy"
          class="elevation-1 w-100"
          density="compact"
          :items-per-page="itemsPerPage"
          :items-per-page-text="t('component.DataTable.itemsPerPageText')"
          @update:options="handleTableOptions"
          @update:sort-by="onHeaderClick($event)"
          must-sort
          :hover="true"
          sort-desc-icon="arrow_upward" sort-asc-icon="arrow_downward"
          :server-items-length="totalItems"
          hide-default-footer
        >

          <template v-slot:item="{ item }">
            <tr >
              <td>{{ item.deviceAgreementNumber }}</td>
              <td>{{ item.displayName }}</td>
              <td>{{ item.employeeId }}</td>
              <td>        <StatusBadge :status="Number(item?.status) || 0" />
              </td>
              <td >
                <v-icon 
                  size="small" 
                  class="mr-2" v-if="actionPermissions( CN_Action.ADMIN )"
                  @click.stop="editRequest(item)"
                  color="primary"
                >
                  edit
                </v-icon>
                <v-icon 
                  size="small" 
                  @click.stop="viewRequest(item)"
                  :color="appStore.currentTheme=='appThemeDark' ? 'white' : 'black'"
                >
                  visibility
                </v-icon>
              </td>
            </tr>
          </template>
          <template v-slot:bottom>
            <div class="d-flex align-center justify-space-between px-4 v-data-table-footer">
              <v-select
                v-model="itemsPerPage"
                :items="[10, 25, 50,100]"
                :label="t('generic.itemPerPage')"
                density="compact"
                variant="outlined"
                hide-details
                class="items-per-page-select"
                style="max-width: 150px;"
                @update:model-value="handlePageSizeChange"
              ></v-select>
              <v-pagination 
                v-model="page" 
                :length="pageCount()" 
                :total-visible="7"
                rounded="circle"
                @update:model-value="onPageChange"
              ></v-pagination>
            </div>
          </template>
        </v-data-table>
  
  </template>
  <style>

/* Custom footer layout */
.custom-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  min-height: 52px;
  border-top: 2px solid rgb(var(--v-theme-on-surface));

}

.footer-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.footer-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.items-per-page-text {
  font-size: 0.875rem;
  color: rgba(var(--v-theme-on-surface), 0.6);
  white-space: nowrap;
}

.items-per-page-select {
  width: 80px;
  min-width: 80px;
}

.items-range-text {
  font-size: 0.875rem;
  color: rgba(var(--v-theme-on-surface), 0.6);
  white-space: nowrap;
}

.page-info {
  font-size: 0.875rem;
  color: rgba(var(--v-theme-on-surface), 0.6);
  margin: 0 8px;
  white-space: nowrap;
}

/* Responsive design */
@media (max-width: 600px) {
  .custom-footer {
    flex-direction: column;
    gap: 12px;
    padding: 16px;
  }

  .footer-left,
  .footer-right {
    width: 100%;
    justify-content: center;
  }
}
</style>
