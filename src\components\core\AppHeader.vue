<script setup lang="ts">
	/**
	 * @file Header component.
	 * @version 1.0.0
	 * @since 1.0.0
	 * <AUTHOR> <<EMAIL>>
	 * @license Copyright (c) 2024 Canon Canada Inc.
	 */

    /**
     * -------
     * Imports
     * -------
     */

	import { computed, ref, watch, onMounted, onUnmounted } from 'vue';
	import { useDisplay  } from 'vuetify';
    import { useAppStore } from '@/stores/AppStore';
    import { useUserStore } from '@/stores/UserStore';
	import UserMenu from './UserMenu.vue';
    import actionPermissions from '@/composables/auth/actionPermission';
    import { CN_Action } from '@/lib/common/types';
    import { useI18n } from 'vue-i18n';
import { useInternalUserStore } from '../../stores/InternalUserStore';

    /**
     * ----
     * Main
     * ----
     */

	// Add stores.
	const appStore  = useAppStore();
    const userStore  = useUserStore();
	const userInternalStore = useInternalUserStore();

    // Add language support.
    const { t } = useI18n();

	// Data
	const { mdAndUp } = useDisplay()
	const drawer = ref(true);
	const appTitle = computed(() => t('app.title'));
	const zoomLevel = ref(100);
	const isOverlayMode = ref(false);





	    // Set the language preference to English.
    const setLanguageEnglish = () =>
    {      
        appStore.setLanguageEnglish();
    }
	watch(appTitle, (newTitle) => {
		document.title = newTitle;
	});
	onMounted(() => {
		document.title = appTitle.value;
	});
	// Set the language preference to French.
    const setLanguageFrench = () =>
    {      
        appStore.setLanguageFrench();
    }

    const getLanguageCode = computed( () =>
    {
        if ( appStore.currentLanguage === 'fr-CA' )
        {
            return 'FR';
        }
        else
        {
            return 'EN';
        }
    });

</script>

<template>
	<v-app-bar color="header" elevation="2" height="52" order="0" app>
		<template v-if="userStore.authenticated && (actionPermissions( CN_Action.ADMIN ) || userInternalStore.approver) && zoomLevel <= 125" v-slot:prepend>
			<v-app-bar-nav-icon color="onHeader" @click.stop="drawer = !drawer"></v-app-bar-nav-icon>
		</template>

		<v-app-bar-title >
			<RouterLink :to="{ name : 'pageHome' }" class="no-link header-title">
				<img width="74"
					src="https://cci0it0general.blob.core.windows.net/public/image/logo/Canon-Trans-Red-100_25.png">
				<span>{{t('app.title')}}</span>
				<!-- <v-icon v-if="appIcon" color="onHeader" :icon="appIcon" size="23px" end></v-icon> -->
			</RouterLink>
		</v-app-bar-title>

		<v-spacer></v-spacer>

		<!-- -------------- -->
		<!-- TOP NAVIGATION -->
		<!-- -------------- -->
		<div v-if="userStore.authenticated && (actionPermissions( CN_Action.ADMIN ) || userInternalStore.approver)" class="d-none d-md-inline-block">
			<!-- <v-btn :to="{ name : 'pageHome' }" exact>{{ t( 'component.appheader.button.home.label' ) }}</v-btn> -->
		</div>

		<!-- <v-btn v-if="actionPermissions( CN_Action.ADMIN )" :to="{ name : 'pageTroubleshoot' }" exact icon
			size="small" class="ml-3">
			<v-icon color="onHeader" size="24" icon="code"></v-icon>
		</v-btn> -->
      <v-menu location="bottom" open-delay="false" close-delay="350">
            <template v-slot:activator="{ props }">
                <v-btn v-bind="props" variant="tonal" color="primary" density="comfortable" class="ml-3" width="32px" min-width="32px">{{ getLanguageCode }}</v-btn>
            </template>

            <v-list density="compact" class="slim-list">
                <v-list-item @click="setLanguageEnglish" link exact :active="appStore.currentLanguage === 'en-US'">{{ t( 'component.appheader.button.language.en.label' ) }}</v-list-item>
                <v-list-item @click="setLanguageFrench" link exact :active="appStore.currentLanguage === 'fr-CA'">{{ t( 'component.appheader.button.language.fr.label' ) }}</v-list-item>
            </v-list>
        </v-menu>
		<user-menu />
	</v-app-bar>

	<v-progress-linear v-if="appStore.pageLoader" indeterminate
		style="top: 52px !important; position: fixed;"></v-progress-linear>

	<!-- ------------------>
	<!-- SIDE NAVIGATION -->
	<!-- --------------- -->
	<v-navigation-drawer 
		v-if="userStore.authenticated && (actionPermissions( CN_Action.ADMIN ) || userInternalStore.approver)" 
		v-model="drawer" 
		location="left" 
		:temporary="isOverlayMode"
		:permanent="!isOverlayMode && mdAndUp"
		disable-resize-watcher
		color="navDrawer" 
		order="1">

		<v-list bg-color="navDrawer">
			<!-- <v-list-item  :to="{ name: 'pageHome' }" exact>
				<template v-slot:prepend dence >
					<v-icon>home</v-icon>
				  </template>
				<v-list-item-title >{{ t('component.appheader.button.home.label') }}</v-list-item-title>
			</v-list-item> -->

			<v-list-item v-if="actionPermissions( CN_Action.ADMIN )" :to="{ name: 'accessories' }" exact>
						<template #prepend>
				<i class="material-symbols-outlined">keyboard</i>
				
			</template>
				<v-list-item-title>{{ t('component.appheader.button.accessories.label') }}</v-list-item-title>
			</v-list-item>
			<v-list-item v-if="actionPermissions( CN_Action.ADMIN )"  :to="{ name: 'legalAgreements' }" exact>
						<template #prepend>
				<i class="material-symbols-outlined">gavel</i>
				
			</template>
				<v-list-item-title>{{ t('component.appheader.button.legal_agreements.label') }}</v-list-item-title>
			</v-list-item>
		<v-list-item  v-if="actionPermissions(CN_Action.ADMIN) || userInternalStore.approver" :to="{ name: 'daList' }" exact>
			<template #prepend>
				<i class="material-symbols-outlined">contract</i>
				
			</template>
				<v-list-item-title>
					{{ t('component.appheader.button.device_agreements.label') }}
				</v-list-item-title>
		</v-list-item>
		

	
		</v-list>
	</v-navigation-drawer>
</template>

<style lang="scss">
	.navDrawerActive
	{
		background : rgb(var(--v-theme-navDrawer));
    	color : rgba(var(--v-theme-onNavDrawer));
	}

	.header-title
	{
		display : flex;
		align-items : center;

		img 
		{
			margin-right : 20px;
		}

		span
		{
			font-size : 18px !important;
			line-height : 18px !important;
			margin-top: 2px;
			cursor : pointer;
            font-weight: 500;
		}

		i
		{
			justify-content : start;
            margin-top: 1px;
            margin-left: 2px;
        }
	}

    .v-toolbar-title
    {
        flex: unset;
    }
	.v-list{
		padding:0;
	}
	.v-list-item__spacer{
		width: 24px !important;
	}
	.v-list-group {
		--list-indent-size: 16px;
		--parent-padding: var(--indent-padding);
		--prepend-width: 5px;
	}
</style>
