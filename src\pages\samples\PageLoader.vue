<script setup lang="ts">
	/**
	 * @file Sample Page: Page loader component.
	 * @version 1.0.0
	 * @since 1.0.0
	 * <AUTHOR> <<EMAIL>>
	 * @license Copyright (c) 2024 Canon Canada Inc.
	 */

    import { onMounted } from 'vue';
    import { useAppStore } from '@/stores/AppStore';

    // Add stores.
    const appStore = useAppStore();

    // Example 1
    const turnOnPageLoader = () =>
    {
        appStore.startPageLoader();
    }

    // Example 2
    const turnOffPageLoader = () =>
    {
        appStore.stopPageLoader();
    }

    // Executes after Vue template is loaded to DOM. Required on all pages.
    // Extend the below functionality to add additional page initializations as needed.
    onMounted ( () =>
    {
        // Required on -ALL- pages to stop page loader.
        appStore.stopPageLoader();
    });
</script>

<template>
	<v-container class="pt-8">
		<v-row class="text-center">
			<v-col cols="12">
				<span class="text-h3">Page Loader</span>
			</v-col>
		</v-row>

		<v-row class="text-left">
			<v-col cols="12">
                <v-col cols="12">
                    <p>The page loader is controlled from methods in the <b>appStore</b> called <b>startPageLoader()</b> and <b>stopPageLoader()</b>. This is a simple on or off and does not have the same complexities as the app loader. In addition, the page loader does not lock the application.</p>
                    <br>
                    <p>Use the page loader when performing tasks that allow the user to continue to work on the page.</p>
                    <br>
                    <p><b>Important</b>: When navigating between routes, the page loader is turned on automatically. Because of this, every page you create must have a call to <b>appStore.stopPageLoader()</b> whenever your page is fully loaded and ready.</p>
                    <br>
                    <v-divider></v-divider>
                    <br>
                    <span class="text-h4">Examples</span><br><br>
    
                    <p>1. Turn on the page loader.</p><br>
                    <v-btn color="primary" @click="turnOnPageLoader">Turn On Page Loader</v-btn>
    
                    <br><br>
    
                    <p>2. Turn off the page loader.</p><br>
                    <v-btn color="primary" @click="turnOffPageLoader">Turn Off Page Loader</v-btn>
                </v-col>
			</v-col>
		</v-row>
	</v-container>
</template>

<style lang="scss"></style>