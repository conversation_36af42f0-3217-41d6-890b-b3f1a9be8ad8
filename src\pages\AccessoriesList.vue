<template>

    <v-card  class="ma-8">
      <v-card-title  class="d-flex justify-space-between align-center">
        <v-row align="center" no-gutters>
          <v-col>
            {{t('component.Accessories.accessoryList') }}
          </v-col>
          <v-col class="text-right">
            <v-btn color="primary" @click="openDialog">
              {{ t('component.Accessories.addAccessory') }}
            </v-btn>
          </v-col>
        </v-row>
      </v-card-title>
    <v-data-table
      :items="translatedAccessories"
      :headers="headers"
      class="elevation-1 custom-data-table"
      density="compact"
      v-model:items-per-page="itemsPerPage"
      v-model:page="currentPage"
    >

      <template v-slot:item.actions="{ item }">
        <v-icon size="small" color="primary"  @click="editAccessory(item)">edit</v-icon>
      </template>

      <!-- Custom footer template to move items per page to the left -->
      <template v-slot:bottom="{ page, pageCount }">
        <div class="custom-footer">
          <!-- Left side: Items per page -->
          <div class="footer-left">
            <span class="items-per-page-text">{{t('generic.itemPerPage')}}</span>
            <v-select
              v-model="itemsPerPage"
              :items="itemsPerPageOptions"
              variant="outlined"
              density="compact"
              hide-details
              class="items-per-page-select"
            ></v-select>
            <span class="items-range-text">
              {{ getFirstItem() }}-{{ getLastItem() }} of {{ accessories.length }}
            </span>
          </div>

          <!-- Right side: Pagination -->
          <div class="footer-right">
            <v-btn
              icon
              variant="text"
              size="small"
              :disabled="page === 1"
              @click="currentPage = page - 1"
            >
              <v-icon>chevron_left</v-icon>
            </v-btn>
            <span class="page-info">{{ page }} of {{ pageCount }}</span>
            <v-btn
              icon
              variant="text"
              size="small"
              :disabled="page === pageCount"
              @click="currentPage = page + 1"
            >
              <v-icon>chevron_right</v-icon>
            </v-btn>
          </div>
        </div>
      </template>
    </v-data-table>
    <AccessoriesAdd v-model="dialog" :accessory="selectedAccessory" @save="fetchData" />
  
  </v-card>
  <ConfirmationDialog
  v-model:isVisible="dialogStore.isConfirmDialogOpen"
  :title="t('generic.deleteItem')"
  :message="t('component.Accessories.confirmDelete')"
  :confirmText="t('generic.confirm')"
  :cancelText="t('generic.cancel')"
  @confirm="handleConfirm"
  @cancel="handleCancel"
/>

</template>

<script setup  lang="ts">
import { ref, onMounted } from 'vue';
import { deleteAccessory, fetchAccessories } from '@/lib/api';
import AccessoriesAdd from '@/components/core/AccessoriesAdd.vue';
import { useI18n } from 'vue-i18n';
import type { Accessory } from '@/lib/api/types';
import { useDialogStore } from '@/stores/DialogStore';
import { useSnackbarStore } from '@/stores/SnackbarStore';
import ConfirmationDialog from '../components/core/ConfirmDialog.vue';
import { useAppStore } from '@/stores/AppStore';
import { computed } from 'vue';

const dialogStore = useDialogStore();
const snackbar= useSnackbarStore()

const accessories = ref<Accessory[]>([]);
  const slectedRow = ref<Accessory>();

const dialog = ref(false);
const selectedAccessory = ref({});
const { t,locale } = useI18n();

// Pagination variables
const itemsPerPage = ref(25);
const currentPage = ref(1);
const itemsPerPageOptions = [25, 50, 100];
const appStore = useAppStore();

// Helper functions for pagination display
const getFirstItem = () => {
  return Math.min((currentPage.value - 1) * itemsPerPage.value + 1, accessories.value.length);
};

const getLastItem = () => {
  return Math.min(currentPage.value * itemsPerPage.value, accessories.value.length);
};

const headers =computed(() => [
  { title: t('component.Accessories.name'), value: 'name',width:"30%" },
  { title: t('component.Accessories.englishName'), value: 'nameEnglish',width:"30%" },
  { title: t('component.Accessories.frenchName'), value: 'nameFrench',width:"30%" },
  { title: t('component.Accessories.active'), value: 'statusTxt',width:"30%" },
  { title: t('component.Accessories.action'), value: 'actions', sortable: false, width:"10%" }
]);

const fetchData = async () => {
  try{
  appStore.startLoader( 'fetchingAccessories', t( 'app.auth.loader.fetchingAccessories' ) );
  const response = await fetchAccessories();
  accessories.value = response.data.map(el=>{return {...el,statusTxt:el.isActive=='Y'?t('component.Accessories.yes'):t('component.Accessories.no')}});
  }
  catch(error:any){
    console.error('Error fetching locations:', error);
  }
  finally{
    appStore.stopLoader('fetchingAccessories');
  }

};
const translatedAccessories = computed(() => {
  return accessories.value.map(el=>{return {...el,statusTxt:el.isActive=='Y'?t('component.Accessories.yes'):t('component.Accessories.no')}});
});
const openDialog = () => {
  selectedAccessory.value = {name: '', nameFrench: '',nameEnglish:'', isActive: 'Y',type:'Add' } as Accessory;
  dialog.value = true;
};

const editAccessory = (item:Accessory) => {
  selectedAccessory.value = { ...item,type:'Edit' };
  dialog.value = true;
};
const deleteAccessoryInit= async (row:Accessory)=> {
  // Logic to delete the selected location
  slectedRow.value=row;
  dialogStore.openConfirmlDialog()
}
const deleteAccessoryById = async (id:number=0) => {
  await deleteAccessory(id);
  fetchData();
};
const handleCancel = () => {
  console.log('Cancelled!');
  // Handle the cancel action here
  dialogStore.closeConfirmDialog()
};
const handleConfirm = async() => {
  console.log('Confirmed!');
  try {
    dialogStore.openConfirmlDialog()
        await deleteAccessoryById(slectedRow.value?.accessoryId as number);
        snackbar.show({
                    text: t( 'app.auth.snack.accessorydeleted' ),
                    timeout: 5000,
                    color: 'success',
                    close: true,
                    icon: 'verified_user'
                })
    } catch (error) {
      snackbar.show({
                    text: t( 'app.auth.snack.accessorydeleteFailed' ),
                    timeout: 5000,
                    color: 'error',
                    close: true,
                    icon: 'error'
                })
        console.error('Error fetching locations:', error);
    }
  // Handle the confirmation action here (e.g., delete an item)
};
onMounted(fetchData);
</script>
