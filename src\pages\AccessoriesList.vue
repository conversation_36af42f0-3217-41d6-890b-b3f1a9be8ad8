<template>

    <v-card>
      <v-card-title  class="d-flex justify-space-between align-center">
        <v-row align="center" no-gutters>
          <v-col>
            {{t('component.Accessories.accessoryList') }}
          </v-col>
          <v-col class="text-right">
            <v-btn color="primary" @click="openDialog">
              {{ t('component.Accessories.addAccessory') }}
            </v-btn>
          </v-col>
        </v-row>
      </v-card-title>
    <v-data-table :items="accessories" :headers="headers" class="elevation-1"
    density="compact">
    
      <template v-slot:item.actions="{ item }">
        <v-icon size="small" color="primary"  @click="editAccessory(item)">edit</v-icon>
        <v-icon size="small" color="error"  @click="deleteAccessoryInit(item)">delete</v-icon>
      </template>
    </v-data-table>
    <AccessoriesAdd v-model="dialog" :accessory="selectedAccessory" @save="fetchData" />
  
  </v-card>
  <ConfirmationDialog
  v-model:isVisible="dialogStore.isConfirmDialogOpen"
  :title="t('generic.deleteItem')"
  :message="t('component.Accessories.confirmDelete')"
  :confirmText="t('generic.confirm')"
  :cancelText="t('generic.cancel')"
  @confirm="handleConfirm"
  @cancel="handleCancel"
/>

</template>

<script setup  lang="ts">
import { ref, onMounted } from 'vue';
import { deleteAccessory, fetchAccessories } from '@/lib/api';
import AccessoriesAdd from '@/components/core/AccessoriesAdd.vue';
import { useI18n } from 'vue-i18n';
import type { Accessory } from '@/lib/api/types';
import { useDialogStore } from '@/stores/DialogStore';
import { useSnackbarStore } from '@/stores/SnackbarStore';
import ConfirmationDialog from '../components/core/ConfirmDialog.vue';

const dialogStore = useDialogStore();
const snackbar= useSnackbarStore()

const accessories = ref<Accessory[]>([]);
  const slectedRow = ref<Accessory>();

const dialog = ref(false);
const selectedAccessory = ref({});
const { t,locale } = useI18n();

const headers = [
  { title: 'Name', value: 'name' },
  { title: 'Description', value: 'description' },
  { title: 'Active', value: 'isActive' },
  { title: 'Actions', value: 'actions', sortable: false }
];

const fetchData = async () => {
  const response = await fetchAccessories();
  accessories.value = response.data;
};

const openDialog = () => {
  selectedAccessory.value = {};
  dialog.value = true;
};

const editAccessory = (item:Accessory) => {
  selectedAccessory.value = { ...item };
  dialog.value = true;
};
const deleteAccessoryInit= async (row:Accessory)=> {
  // Logic to delete the selected location
  slectedRow.value=row;
  dialogStore.openConfirmlDialog()
}
const deleteAccessoryById = async (id:number=0) => {
  await deleteAccessory(id);
  fetchData();
};
const handleCancel = () => {
  console.log('Cancelled!');
  // Handle the cancel action here
  dialogStore.closeConfirmDialog()
};
const handleConfirm = async() => {
  console.log('Confirmed!');
  try {
    dialogStore.openConfirmlDialog()
        await deleteAccessoryById(slectedRow.value?.accessoryId as number);
        snackbar.show({
                    text: t( 'app.auth.snack.locationSaveSuccess' ),
                    timeout: 5000,
                    color: 'success',
                    close: true,
                    icon: 'verified_user'
                })
    } catch (error) {
      snackbar.show({
                    text: t( 'app.auth.snack.locationSaveFailed' ),
                    timeout: 5000,
                    color: 'error',
                    close: true,
                    icon: 'error'
                })
        console.error('Error fetching locations:', error);
    }
  // Handle the confirmation action here (e.g., delete an item)
};
onMounted(fetchData);
</script>