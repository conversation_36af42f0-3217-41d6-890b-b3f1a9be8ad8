<template>

    <v-card  class="ma-8">
      <v-card-title  class="d-flex justify-space-between align-center">
        <v-row align="center" no-gutters>
          <v-col>
            {{t('component.Accessories.accessoryList') }}
          </v-col>
          <v-col class="text-right">
            <v-btn color="primary" @click="openDialog">
              {{ t('component.Accessories.addAccessory') }}
            </v-btn>
          </v-col>
        </v-row>
      </v-card-title>

      <!-- Tabs for Active/Inactive -->
      <v-tabs v-model="activeTab" class="px-4">
        <v-tab value="active">
          {{ t('component.Accessories.active') }} ({{ activeAccessories.length }})
        </v-tab>
        <v-tab value="inactive">
          {{ t('component.Accessories.inactive') }} ({{ inactiveAccessories.length }})
        </v-tab>
      </v-tabs>

      <!-- Tab Content -->
      <v-tabs-window v-model="activeTab">
        <!-- Active Accessories Tab -->
        <v-tabs-window-item value="active">
          <v-data-table
            :items="activeAccessories"
            :headers="headers"
            class="elevation-1 custom-data-table"
            density="compact"
            v-model:items-per-page="itemsPerPage"
            v-model:page="currentPageActive"
          >
            <template v-slot:item.actions="{ item }">
              <v-icon size="small" color="primary"  @click="editAccessory(item)">edit</v-icon>
            </template>
                  <template v-slot:bottom="{ page, pageCount }">
        <div class="custom-footer">
          <!-- Left side: Items per page -->
          <div class="footer-left">
            <span class="items-per-page-text">{{t('generic.itemPerPage')}}</span>
            <v-select
              v-model="itemsPerPage"
              :items="itemsPerPageOptions"
              variant="outlined"
              density="compact"
              hide-details
              class="items-per-page-select"
            ></v-select>
            <span class="items-range-text">
              {{ getFirstItem(currentPageActive,activeAccessories) }}-{{ getLastItem(currentPageActive,activeAccessories) }} of {{ activeAccessories.length }}
            </span>
          </div>

          <!-- Right side: Pagination -->
          <div class="footer-right">
            <v-btn
              icon
              variant="text"
              size="small"
              :disabled="page === 1"
              @click="currentPageActive = page - 1"
            >
              <v-icon>chevron_left</v-icon>
            </v-btn>
            <span class="page-info">{{ page }} of {{ pageCount }}</span>
            <v-btn
              icon
              variant="text"
              size="small"
              :disabled="page === pageCount"
              @click="currentPageActive = page + 1"
            >
              <v-icon>chevron_right</v-icon>
            </v-btn>
          </div>
        </div>
      </template>
          </v-data-table>
        </v-tabs-window-item>

        <!-- Inactive Accessories Tab -->
        <v-tabs-window-item value="inactive">
          <v-data-table
            :items="inactiveAccessories"
            :headers="headers"
            class="elevation-1 custom-data-table"
            density="compact"
            v-model:items-per-page="itemsPerPage"
            v-model:page="currentPageInactive"
          >
            <template v-slot:item.actions="{ item }">
              <v-icon size="small" color="primary"  @click="editAccessory(item)">edit</v-icon>
            </template>
                            <template v-slot:bottom="{ page, pageCount }">
        <div class="custom-footer">
          <!-- Left side: Items per page -->
          <div class="footer-left">
            <span class="items-per-page-text">{{t('generic.itemPerPage')}}</span>
            <v-select
              v-model="itemsPerPage"
              :items="itemsPerPageOptions"
              variant="outlined"
              density="compact"
              hide-details
              class="items-per-page-select"
            ></v-select>
            <span class="items-range-text">
              {{ getFirstItem(currentPageInactive,inactiveAccessories) }}-{{ getLastItem(currentPageInactive,inactiveAccessories) }} of {{ inactiveAccessories.length }}
            </span>
          </div>

          <!-- Right side: Pagination -->
          <div class="footer-right">
            <v-btn
              icon
              variant="text"
              size="small"
              :disabled="page === 1"
              @click="currentPageInactive = page - 1"
            >
              <v-icon>chevron_left</v-icon>
            </v-btn>
            <span class="page-info">{{ page }} of {{ pageCount }}</span>
            <v-btn
              icon
              variant="text"
              size="small"
              :disabled="page === pageCount"
              @click="currentPageInactive = page + 1"
            >
              <v-icon>chevron_right</v-icon>
            </v-btn>
          </div>
        </div>
      </template>
          </v-data-table>
        </v-tabs-window-item>
      </v-tabs-window>
    </v-card>

    <AccessoriesAdd v-model="dialog" :accessory="selectedAccessory" @save="fetchData" />
  <ConfirmationDialog
  v-model:isVisible="dialogStore.isConfirmDialogOpen"
  :title="t('generic.deleteItem')"
  :message="t('component.Accessories.confirmDelete')"
  :confirmText="t('generic.confirm')"
  :cancelText="t('generic.cancel')"
  @confirm="handleConfirm"
  @cancel="handleCancel"
/>

</template>

<script setup  lang="ts">
import { ref, onMounted } from 'vue';
import { deleteAccessory, fetchAccessories } from '@/lib/api';
import AccessoriesAdd from '@/components/core/AccessoriesAdd.vue';
import { useI18n } from 'vue-i18n';
import type { Accessory } from '@/lib/api/types';
import { useDialogStore } from '@/stores/DialogStore';
import { useSnackbarStore } from '@/stores/SnackbarStore';
import ConfirmationDialog from '../components/core/ConfirmDialog.vue';
import { useAppStore } from '@/stores/AppStore';
import { computed } from 'vue';

const dialogStore = useDialogStore();
const snackbar= useSnackbarStore()

const accessories = ref<Accessory[]>([]);
const slectedRow = ref<Accessory>();

// Tab management
const activeTab = ref('active');

const dialog = ref(false);
const selectedAccessory = ref({});
const { t,locale } = useI18n();

// Pagination variables
const itemsPerPage = ref(10);
const currentPageActive = ref(1);
const currentPageInactive = ref(1);
const itemsPerPageOptions = [10,25, 50, 100];
const appStore = useAppStore();

// Helper functions for pagination display
const getFirstItem = (page:any,items:any) => {

  return Math.min((page - 1) * itemsPerPage.value + 1, items?.length);
};

const getLastItem = (page:any,items:any) => {
  return Math.min(page * itemsPerPage.value, items?.length);
};

const headers =computed(() => [
  { title: t('component.Accessories.name'), value: 'name',width:"30%" },
  { title: t('component.Accessories.englishName'), value: 'nameEnglish',width:"30%" },
  { title: t('component.Accessories.frenchName'), value: 'nameFrench',width:"30%" },
  { title: t('component.Accessories.active'), value: 'statusTxt',width:"30%" },
  { title: t('component.Accessories.action'), value: 'actions', sortable: false, width:"10%" }
]);

const fetchData = async () => {
  try{
  appStore.startLoader( 'fetchingAccessories', t( 'app.auth.loader.fetchingAccessories' ) );
  const response = await fetchAccessories();
  accessories.value = response.data.map(el=>{return {...el,statusTxt:el.isActive=='Y'?t('component.Accessories.yes'):t('component.Accessories.no')}});
  }
  catch(error:any){
    console.error('Error fetching locations:', error);
  }
  finally{
    appStore.stopLoader('fetchingAccessories');
  }

};
const translatedAccessories = computed(() => {
  return accessories.value.map(el=>{return {...el,statusTxt:el.isActive=='Y'?t('component.Accessories.yes'):t('component.Accessories.no')}});
});

// Computed properties for active and inactive accessories
const activeAccessories = computed(() => {
  return accessories.value
    .filter(el => el.isActive === 'Y')
    .map(el => ({...el, statusTxt: t('component.Accessories.yes')}));
});

const inactiveAccessories = computed(() => {
  return accessories.value
    .filter(el => el.isActive === 'N')
    .map(el => ({...el, statusTxt: t('component.Accessories.no')}));
});
const openDialog = () => {
  selectedAccessory.value = {name: '', nameFrench: '',nameEnglish:'', isActive: 'Y',type:'Add' } as Accessory;
  dialog.value = true;
};

const editAccessory = (item:Accessory) => {
  selectedAccessory.value = { ...item,type:'Edit' };
  dialog.value = true;
};
const deleteAccessoryInit= async (row:Accessory)=> {
  // Logic to delete the selected location
  slectedRow.value=row;
  dialogStore.openConfirmlDialog()
}
const deleteAccessoryById = async (id:number=0) => {
  await deleteAccessory(id);
  fetchData();
};
const handleCancel = () => {
  console.log('Cancelled!');
  // Handle the cancel action here
  dialogStore.closeConfirmDialog()
};
const handleConfirm = async() => {
  console.log('Confirmed!');
  try {
    dialogStore.openConfirmlDialog()
        await deleteAccessoryById(slectedRow.value?.accessoryId as number);
        snackbar.show({
                    text: t( 'app.auth.snack.accessorydeleted' ),
                    timeout: 5000,
                    color: 'success',
                    close: true,
                    icon: 'verified_user'
                })
    } catch (error) {
      snackbar.show({
                    text: t( 'app.auth.snack.accessorydeleteFailed' ),
                    timeout: 5000,
                    color: 'error',
                    close: true,
                    icon: 'error'
                })
        console.error('Error fetching locations:', error);
    }
  // Handle the confirmation action here (e.g., delete an item)
};
onMounted(fetchData);
</script>

<style scoped>
.custom-data-table {
  border: 1px solid #e0e0e0;
}

.custom-data-table .v-data-table__wrapper {
  border-radius: 4px;
}

/* Tab styling */
.v-tabs {
  border-bottom: 1px solid #e0e0e0;
}

.v-tab {
  text-transform: none !important;
  font-weight: 500;
}

.v-tabs-window-item {
  padding: 0;
}

/* Data table within tabs */
.v-tabs-window-item .v-data-table {
  border: none;
  box-shadow: none;
}
</style>
