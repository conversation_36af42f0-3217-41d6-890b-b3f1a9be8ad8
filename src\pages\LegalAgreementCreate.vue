<template>
  <v-container>
    <v-row justify="center">
      <v-col cols="12" md="10" lg="8">
        <LegalAgreementForm 
          @save="handleSave" 
          @cancel="navigateBack"
        />
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';
import { useSnackbarStore } from '@/stores/SnackbarStore';
import LegalAgreementForm from '@/components/legal/LegalAgreementForm.vue';
import { saveLegalAgreement } from '@/lib/api';
import type { LegalAgreement } from '@/lib/api/types';
import { useAppStore } from '@/stores/AppStore';
import { useI18n } from 'vue-i18n';

const router = useRouter();
const snackbar = useSnackbarStore();
const appStore = useAppStore();
const { t } = useI18n();

const handleSave = async (formData: LegalAgreement) => {
  try {
    appStore.startLoader('savingLegalAgreement', t('app.auth.loader.savingLegalAgreement'));
    
    // Call API to save the legal agreement
    formData.legalId=null;
    await saveLegalAgreement(formData);
    
    // Show success message
    snackbar.show({
      text: t('legal.messages.createSuccess'),
      color: 'success',
      timeout: 3000
    });
    
    // Navigate back to legal agreements list
    navigateBack();
  } catch (error) {
    console.error('Error saving legal agreement:', error);
    snackbar.show({
      text: `${t('legal.messages.createError')}: ${error instanceof Error ? error.message : t('common.unknownError')}`,
      color: 'error',
      timeout: 5000
    });
  } finally {
    appStore.stopLoader('savingLegalAgreement');
  }
};

const navigateBack = () => {
  router.push('/legal-agreements');
};
</script>

