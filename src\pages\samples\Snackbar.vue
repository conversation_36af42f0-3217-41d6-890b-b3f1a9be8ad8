<script setup lang="ts">
	/**
	 * @file Sample Page: Snackbar component.
	 * @version 1.0.0
	 * @since 1.0.0
	 * <AUTHOR> <<EMAIL>>
	 * @license Copyright (c) 2022 Canon Canada Inc.
	 */

    /**
     * -------
     * Imports
     * -------
     */

	import { useSnackbarStore } from "@/stores/SnackbarStore";
    import { useAppStore } from '@/stores/AppStore';
    import { onMounted } from 'vue';

    /**
     * ----
     * Main
     * ----
     */

    // Add stores.
    const appStore = useAppStore();
	const snackbarStore = useSnackbarStore();

    // Executes after Vue template is loaded to DOM. Required on all pages.
    // Extend the below functionality to add additional page initializations as needed.
    onMounted ( () =>
    {
        // Required on -ALL- pages to stop page loader.
        appStore.stopPageLoader();
    });

	const snackBasic = () =>
    {      
		snackbarStore.show
		({
            text : 'This is snackbar text.',
			close : true
        });
    }

    const snackSlim = () =>
    {      
		snackbarStore.show
		({
            text : 'This is snackbar text.',
			close : true,
            slim: true
        });
    }

	const snackNoClose = () =>
    {      
		snackbarStore.show
		({
            text : 'This is snackbar has no close button but will disappear in 5 seconds.',
			timeout : 5000,
			color : 'primary',
			close : false
        });
    }

	const snackInfo = () =>
    {      
		snackbarStore.show
		({
            text : 'This is snackbar which is Information themed.',
			close : true,
			color : 'info'
        });
    }

    const snackBasicIcon = () =>
    {      
		snackbarStore.show
		({
            text : 'This is snackbar text.',
			close : true,
            icon: 'check'
        });
    }

	const snackNoCloseIcon = () =>
    {      
		snackbarStore.show
		({
            text : 'This is snackbar has no close button but will disappear in 5 seconds.',
			timeout : 5000,
			color : 'primary',
			close : false,
            icon: 'hourglass_empty'
        });
    }

	const snackInfoIcon = () =>
    {      
		snackbarStore.show
		({
            text : 'This is snackbar which is Information themed.',
			close : true,
			color : 'info',
            icon: 'palette'
        });
    }

	const snackContained = () =>
    {      
		snackbarStore.show
		({
            text : 'This is contained snackbar.',
			close : true,
			color : 'primary',
			variant : undefined
        });
    }

	const snackContainedFlat = () =>
    {      
		snackbarStore.show
		({
            text : 'This is contained flat snackbar.',
			close : true,
			color : 'primary',
			variant : 'flat'
        });
    }

	const snackContainedText = () =>
    {      
		snackbarStore.show
		({
            text : 'This is text snackbar. It is also offset.',
			close : true,
			color : 'primary',
			variant : 'text'
        });
    }

	const snackOutlined = () =>
    {      
		snackbarStore.show
		({
            text : 'This is outlined snackbar.',
			close : true,
			color : 'primary',
			variant : 'outlined'
        });
    }

	const snackPlain = () =>
    {      
		snackbarStore.show
		({
            text : 'This is plain snackbar.',
			close : true,
			color : 'primary',
			variant : 'plain'
        });
    }

	const snackText = () =>
    {      
		snackbarStore.show
		({
            text : 'This is text snackbar.',
			close : true,
			color : 'primary',
			variant : 'text'
        });
    }

	const snackTop = () =>
    {      
		snackbarStore.show
		({
            text : 'This is located on the top.',
			close : true,
			color : 'primary',
			location : 'top'
        });
    }

	const snackBottom = () =>
    {      
		snackbarStore.show
		({
            text : 'This is located on the bottom.',
			close : true,
			color : 'primary',
			location : 'bottom'
        });
    }

	const snackLeft = () =>
    {      
		snackbarStore.show
		({
            text : 'This is located on the left.',
			close : true,
			color : 'primary',
			location : 'left'
        });
    }

	const snackRight = () =>
    {      
		snackbarStore.show
		({
            text : 'This is located on the right.',
			close : true,
			color : 'primary',
			location : 'right'
        });
    }

	const snackCenter = () =>
    {      
		snackbarStore.show
		({
            text : 'This is located on the center.',
			close : true,
			color : 'primary',
			location : 'center'
        });
    }

	const snackTopRight = () =>
    {      
		snackbarStore.show
		({
            text : 'This is located on the center and right.',
			close : true,
			color : 'primary',
			location : 'top right'
        });
    }

	const snackBottomLeft = () =>
    {      
		snackbarStore.show
		({
            text : 'This is located on the bottom and left.',
			close : true,
			color : 'primary',
			location : 'bottom left'
        });
    }

	const snackTopLeft = () =>
    {      
		snackbarStore.show
		({
            text : 'This is located on the center and right.',
			close : true,
			color : 'primary',
			location : 'top left'
        });
    }
</script>

<template>
	<v-container class="pt-8">

		<v-row class="text-center pb-5">
			<v-col cols="12">
				<span class="text-h3">Snackbar</span>
			</v-col>
		</v-row>

		<v-row>
			<v-col cols="12">
                <p>Displaying messages to users that do not impact the users ability to continue using the app is the job of snackbar messages.</p>
                <br>
                <p>The snackbar component standardizes the use of the Vuetify snackbar feature.</p>

				<v-card width="100%" class="mt-6">
					<v-card-title>Examples</v-card-title>

					<span class="pa-4">The app snackbar component displays short, quick messages to users that can disappear on their own.</span>

					<v-card-text>
						<v-row class="text-center mb-4">
							<v-col cols="12" md="3">
								<v-btn @click="snackBasic()" size="small" block>Basic Snackbar</v-btn>
							</v-col>
							<v-col cols="12" md="3">
								<v-btn @click="snackNoClose()" color="primary" size="small" block>Snackbar No Close</v-btn>
							</v-col>
							<v-col cols="12" md="3">
								<v-btn @click="snackInfo()" color="info" size="small" block>Info Snackbar</v-btn>
							</v-col>
                            <v-col cols="12" md="3">
								<v-btn @click="snackSlim()" color="info" size="small" block>Slim Snackbar</v-btn>
							</v-col>
						</v-row>

                        <span class="text-overline d-flex justify-center">Icons</span>

                        <v-row class="text-center my-4">
							<v-col cols="12" md="3">
								<v-btn @click="snackBasicIcon()" size="small" block>Basic Snackbar (With Icon)</v-btn>
							</v-col>
							<v-col cols="12" md="3">
								<v-btn @click="snackNoCloseIcon()" color="primary" size="small" block>Snackbar No Close (With Icon)</v-btn>
							</v-col>
							<v-col cols="12" md="3">
								<v-btn @click="snackInfoIcon()" color="info" size="small" block>Info Snackbar (With Icon)</v-btn>
							</v-col>
						</v-row>

						<span class="text-overline d-flex justify-center">Variants</span>

						<v-row class="text-center my-4">
							<v-col cols="12" md="3">
								<v-btn @click="snackContained()" color="primary" size="small" block>Contained</v-btn>
							</v-col>
							<v-col cols="12" md="3">
								<v-btn @click="snackContainedFlat()" color="primary" size="small" block>Contained-flat</v-btn>
							</v-col>
							<v-col cols="12" md="3">
								<v-btn @click="snackContainedText()" color="primary" size="small" block>Contained-text</v-btn>
							</v-col>
							<v-col cols="12" md="3">
								<v-btn @click="snackOutlined()" color="primary" size="small" block>Outlined</v-btn>
							</v-col>
						</v-row>

						<v-row class="text-center mb-4">
							<v-col cols="12" md="3">
								<v-btn @click="snackPlain()" color="primary" size="small" block>Plain</v-btn>
							</v-col>
							<v-col cols="12" md="3">
								<v-btn @click="snackText()" color="primary" size="small" block>Text</v-btn>
							</v-col>
						</v-row>

						<span class="text-overline d-flex justify-center">Locations</span>

						<v-row class="text-center mt-4">
							<v-col cols="12" md="3">
								<v-btn @click="snackTop()" color="primary" size="small" block>Top</v-btn>
							</v-col>
							<v-col cols="12" md="3">
								<v-btn @click="snackBottom()" color="primary" size="small" block>Bottom</v-btn>
							</v-col>
							<v-col cols="12" md="3">
								<v-btn @click="snackLeft()" color="primary" size="small" block>Left</v-btn>
							</v-col>
							<v-col cols="12" md="3">
								<v-btn @click="snackRight()" color="primary" size="small" block>Right</v-btn>
							</v-col>
						</v-row>

						<v-row class="text-center mt-4">
							<v-col cols="12" md="3">
								<v-btn @click="snackCenter()" color="primary" size="small" block>Center</v-btn>
							</v-col>
							<v-col cols="12" md="3">
								<v-btn @click="snackTopRight()" color="primary" size="small" block>Top and Right</v-btn>
							</v-col>
							<v-col cols="12" md="3">
								<v-btn @click="snackBottomLeft()" color="primary" size="small" block>Bottom and Left</v-btn>
							</v-col>
							<v-col cols="12" md="3">
								<v-btn @click="snackTopLeft()" color="primary" size="small" block>Top and Left</v-btn>
							</v-col>
						</v-row>
					</v-card-text>
				</v-card>
			</v-col>
		</v-row>
	</v-container>
</template>

<style lang="scss"></style>