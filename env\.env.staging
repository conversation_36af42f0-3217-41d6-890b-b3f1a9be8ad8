###
# @file Staging environment variables.
# @version 1.0.0
# @since 1.0.0
# <AUTHOR> <<EMAIL>>
# @license Copyright (c) 2024 Canon Canada Inc.
##

# Flag indicating if application is running in development mode.
VITE_APP_DEV="FALSE"

# Name of the application which will appear in the header and footer.
VITE_APP_TITLE="Device Agreement"

# Version of the application which will appear in the footer.
VITE_APP_VERSION="0.0.1 (STAGING)"

# Icon code of the application that will appear in the header.
VITE_APP_ICON="double_arrow"

# Flag indicating if theme controls should be enabled to the user.
VITE_APP_CONTROL_THEME="TRUE"

# Azure App Registration Client ID for this application.
VITE_APP_AUTH_CLIENT_ID="ab482ebb-7b67-4ecb-9fca-c52ba8f8fe7f"

# Canon USA Azure tenant ID.
VITE_APP_AUTH_TENANT_ID="5132013a-1c8d-4f87-9f4d-43110f5bc07b"

# Root URL of the application.
VITE_APP_AUTH_REDIRECT_URL="https://brm-supra-dev.cci.canon.info/device-agreement-uat/"

# Exposed API scope for general backend access.
VITE_APP_API_SCOPE="api://ab482ebb-7b67-4ecb-9fca-c52ba8f8fe7f/App.Standard"

# Base URL of the local app API.
VITE_APP_API_BASE_URL="https://brm-supra-dev.cci.canon.info/canon-device-agreement-api-uat"
