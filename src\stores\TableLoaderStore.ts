/**
 * @file Pinia store defining global state for messages that occur in the application.
 * @version 1.0.0
 * @since 1.0.0
 * <AUTHOR> <<EMAIL>>
 * @license Copyright (c) 2024 Canon Canada Inc.
 */

/**
 * -------
 * Imports
 * -------
 */

import { defineStore } from 'pinia';
import type { TableLoaderState } from '@/lib/common/types';

/**
 * ------
 * Export
 * ------
 */

export const useTableLoader = defineStore( 'TableLoader',
{
    /**
	 * Store state variables.
	 */
    state : () : TableLoaderState =>
    {
        return {
            loading:false,
            pageSize:50,
            page:0
        }
    },

    /**
	 * Store actions.
	 */
    actions :
    {
        /**
         * Display table loader.
         * 
         */
        showLoading () : void
        {
            // Add message to the queue to display.
            this.loading=true;
        },

        /**
         * Hides table loader
         */
        hideLoading ()
        {
            this.loading=false
        },

           /**
         * Set Page Size
         */
           setPageSize(size:number)
           {
               this.pageSize=size
           },

        /**
        * Set Page Number
        */
        setPageNumber(size: number) {
            this.page = size
        }
    }
}); 