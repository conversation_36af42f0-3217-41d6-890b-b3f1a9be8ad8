/**
 * @file Login process for application.
 * @version 1.0.0
 * @since 1.0.0
 * <AUTHOR> <<EMAIL>>
 * @@license Copyright (c) 2024 Canon Canada Inc.
 */

/**
 * -------
 * Imports
 * -------
 */

import { useAppStore } from '@/stores/AppStore';
import { useMessageStore } from '@/stores/MessageStore';
import { useSnackbarStore } from "@/stores/SnackbarStore";
import { CanonAuth } from '@/lib/canonAuth';
import { CanonAuthState } from '@/lib/common/types';
import { MessageType } from '@/lib/common/types';
import useSetAuthState from '@/composables/auth/setAuthState';

/**
 * ----
 * Export
 * ----
*/

/**
 * Performs login tasks.
 *
 * @param {any} [t] VueI18n object for translation.
 */
export default async ( t : any ) : Promise <boolean> =>
{
    // Add stores.
    const appStore = useAppStore();
    const messageStore = useMessageStore();
    const snackbarStore = useSnackbarStore();

    // Perform login tasks for authentication.
    const loginResult : boolean = await CanonAuth.doLogin();

    if ( loginResult )
    {
        if ( CanonAuth.authState === CanonAuthState.REDIRECTED )
        {
            appStore.startLoader( 'Redirect', t( 'app.auth.loader.redirect' ) );
        }
        else
        {
            appStore.startLoader( 'SetAuthState', t( 'app.auth.loader.authenticating' ) );

            const setAuthState = useSetAuthState;

            // Update the login state for the app.
            const authResponse = await setAuthState();

            appStore.stopLoader( 'SetAuthState' );

            // If auth state was successful (logged in OK).
            if ( authResponse )
            {
                snackbarStore.show
                ({
                    text: t( 'app.auth.snack.success' ),
                    timeout: 5000,
                    color: 'success',
                    close: true,
                    icon: 'verified_user'
                });

                return true;
            }
            else
            {
                if ( CanonAuth.authState === CanonAuthState.NO_LOGIN )
                {
                    messageStore.show
                    ({
                        type: MessageType.ERROR,
                        title: t( 'app.auth.error.title_2' ),
                        body: t( 'app.auth.error.desc_2' ),
                        subtitle: t( 'app.auth.error.sub_1' ),
                        showContactIT: false,
                        btnClose: true,
                        btnRefresh: true,
                        disableApp: false,
                        icon: 'lock'
                    });
                }
                // Users account is disabled.
                if ( CanonAuth.authState === CanonAuthState.DISABLED )
                {
                    messageStore.show
                    ({
                        type: MessageType.ERROR,
                        title: t( 'app.auth.error.title_7' ),
                        body: t( 'app.auth.error.desc_7' ),
                        showContactIT: false,
                        btnClose: false,
                        btnRefresh: false,
                        disableApp: true,
                        icon: 'lock'
                    });
                }
                // User logged in but has no access to the app.
                else if ( CanonAuth.authState === CanonAuthState.NO_ACCESS )
                {
                    messageStore.show
                    ({
                        type: MessageType.ERROR,
                        title: t( 'app.auth.error.title_3' ),
                        body: t( 'app.auth.error.desc_3' ),
                        subtitle: t( 'app.auth.error.sub_1' ),
                        showContactIT: false,
                        btnClose: true,
                        btnRefresh: false,
                        disableApp: false,
                        icon: 'lock'
                    });
                }
                else if ( CanonAuth.authState === CanonAuthState.SESSION_EXPIRED )
                {
                    messageStore.show
                    ({
                        type: MessageType.ERROR,
                        title: t( 'app.auth.error.title_6' ),
                        subtitle: t( 'app.auth.error.desc_6' ),
                        body: t( 'app.auth.error.sub_3' ),
                        btnCustom:
                        [
                            {
                                text: 'Sign-In',
                                colour: 'primary',
                                callback: async () =>
                                {
                                    appStore.startLoader( 'Login', t( 'app.auth.loader.logging') );

                                    await CanonAuth.doLoginRedirect();
                                },
                                close: true
                            }
                        ],
                        showContactIT: false,
                        btnClose: false,
                        btnRefresh: false,
                        disableApp: false
                    });
                }
                // An error occurred.
                else
                {
                    messageStore.show
                    ({
                        type: MessageType.ERROR,
                        title: t( 'app.auth.error.title_5' ),
                        body: t( 'app.auth.error.desc_4' ),
                        showContactIT: false,
                        btnClose: true,
                        btnRefresh: false,
                        disableApp: false
                    });
                }
            }
        }
    }
    else
    {
        // Critical error occurred - This state should never happen - Fail safe message.
        if ( CanonAuth.authState === CanonAuthState.NO_LOGIN )
        {
            messageStore.show
            ({
                type: MessageType.ERROR,
                title: t( 'app.auth.error.title_4' ),
                body: t( 'app.auth.error.desc_3' ),
                showContactIT: true,
                btnClose: false,
                btnRefresh: true,
                disableApp: false
            });
        }
        // An error occurred.
        else
        {
            appStore.startLoader( 'Login', t( 'app.auth.loader.logging') );

            await CanonAuth.doLoginRedirect();
        }
    }

    return false;
};