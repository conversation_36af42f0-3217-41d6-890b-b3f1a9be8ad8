<template>
    <v-dialog :model-value="modelValue" persistent @update:modelValue="$emit('update:modelValue', $event)" max-width="600px">
        <v-card>
        <v-card-title>{{ accessory?.type=='Edit' ? t('component.AccessoriesAdd.editTitle') : t('component.AccessoriesAdd.addTitle') }}</v-card-title>
        <v-card-text>
          <v-form ref="formRef" v-model="isFormValid">
            <v-text-field
              v-model="form.name"
              :label="t('component.AccessoriesAdd.nameLabel')"
              :rules="nameRules"
              required
              variant="outlined"
              density="compact"
            ></v-text-field>
            <v-text-field
              v-model="form.nameEnglish"
              :label="t('component.AccessoriesAdd.englishAccessoryLabel')"
              :rules="descriptionRules"
              variant="outlined"
              density="compact"
            ></v-text-field>
                     <v-text-field
              v-model="form.nameFrench"
              :label="t('component.AccessoriesAdd.frenchAccessoryLabel')"
              :rules="descriptionRules"
              variant="outlined"
              density="compact"
            ></v-text-field>
            <v-switch
              v-model="form.isActive"
              :label="statusAccessory"
              :disabled=" accessory?.type=='Add'"
              true-value="Y"
              false-value="N"
              class="custom-switch"
              @update:model-value="updateStatusLabel"
            ></v-switch>
          </v-form>
        </v-card-text>
        <v-card-actions>
          <v-btn color="grey" @click="$emit('update:modelValue', false)">{{ t('component.AccessoriesAdd.cancelButton') }}</v-btn>
          <v-btn color="primary" @click="saveAccessory">{{ t('component.AccessoriesAdd.saveButton') }}</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </template>
  
  <script setup lang="ts">
  import { createAccessory, updateAccessory } from '@/lib/api';
import type { Accessory } from '@/lib/api/types';
import { useAppStore } from '@/stores/AppStore';
import { ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
const appStore = useAppStore();
const { t } = useI18n();

  const props = defineProps({
    modelValue: Boolean,
    accessory: Object
  });
  const emit = defineEmits(['update:modelValue', 'save']);
  const form = ref({ name: '', nameEnglish: '',nameFrench:'', isActive: 'Y' } as Accessory);
  const statusAccessory = ref('');

  // Function to update status label based on switch value
  const updateStatusLabel = (value: string | null) => {
    statusAccessory.value = value === 'Y' ? t('component.Accessories.active') : t('component.Accessories.inactive');
  };

  // Initialize the label with the default form value
  updateStatusLabel(form.value.isActive);

  // Form validation
  const formRef = ref();
  const isFormValid = ref(false);

  // Validation rules
  const nameRules = [
    (v: string) => !!v || t('component.AccessoriesAdd.validation.nameRequired'),
    (v: string) => (v && v.length >= 2) || t('component.AccessoriesAdd.validation.nameMinLength'),
    (v: string) => (v && v.length <= 50) || t('component.AccessoriesAdd.validation.nameMaxLength')
  ];

  const descriptionRules = [
    (v: string) => (!v || v.length <= 200) || t('component.AccessoriesAdd.validation.descriptionMaxLength')
  ];

  watch(() => props.accessory, (newVal:any) => {
    form.value = newVal ? { ...newVal } : { name: '', description: '', isActive: 'Y',type:'Add' };
    // Update status label based on the form value
    updateStatusLabel(form.value.isActive);
    // Reset form validation when accessory changes
    if (formRef.value) {
      formRef.value.resetValidation();
    }
  }, { immediate: true });

  // Reset validation when dialog opens
  watch(() => props.modelValue, (newVal) => {
    if (newVal && formRef.value) {
      formRef.value.resetValidation();
    }
  });
  
  const saveAccessory = async () => {
    // Validate form before submitting
    const { valid } = await formRef.value.validate();

    if (!valid) {
      return; // Stop if form is invalid
    }

    appStore.startLoader( 'savingAccessory', t( 'app.auth.loader.savingAccessory' ) );

    try {
      if (form.value.accessoryId) {
        await updateAccessory(form.value);
      } else {
        await createAccessory(form.value);
      }

      emit('update:modelValue', false);
      emit('save');
    } catch (error) {
      console.error('Error saving accessory:', error);
    } finally {
      appStore.stopLoader( 'savingAccessory');
    }
  };
  </script>

<style scoped>
/* Custom switch styling to match the screenshot */
.custom-switch :deep(.v-switch__track) {
  /* Inactive state - red background */
  background-color: #ffcdd2 !important;
  opacity: 1 !important;
}

.custom-switch :deep(.v-switch__thumb) {
  /* Inactive state - red thumb */
  background-color: #f44336 !important;
}

/* Active state styling */
.custom-switch :deep(.v-selection-control--dirty .v-switch__track) {
  /* Active state - green background */
  background-color: #c8e6c9 !important;
  opacity: 1 !important;
}

.custom-switch :deep(.v-selection-control--dirty .v-switch__thumb) {
  /* Active state - green thumb */
  background-color: #4caf50 !important;
}

/* Hover effects */
.custom-switch :deep(.v-switch__thumb):hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* Focus effects */
.custom-switch :deep(.v-selection-control--focused .v-switch__thumb) {
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2);
}

.custom-switch :deep(.v-selection-control:not(.v-selection-control--dirty).v-selection-control--focused .v-switch__thumb) {
  box-shadow: 0 0 0 3px rgba(244, 67, 54, 0.2);
}

/* Transition for smooth color changes */
.custom-switch :deep(.v-switch__track),
.custom-switch :deep(.v-switch__thumb) {
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
}
</style>