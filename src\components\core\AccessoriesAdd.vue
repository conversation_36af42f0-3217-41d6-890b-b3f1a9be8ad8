<template>
    <v-dialog :model-value="modelValue" persistent @update:modelValue="$emit('update:modelValue', $event)" max-width="600px">
        <v-card>
        <v-card-title>{{ accessory ? 'Edit' : 'Add' }} Accessory</v-card-title>
        <v-card-text>
          <v-text-field v-model="form.name" label="Name" required></v-text-field>
          <v-text-field v-model="form.description" label="Description" required></v-text-field>
          <v-switch v-model="form.isActive" label="Active" true-value="Y" false-value="N"></v-switch>
        </v-card-text>
        <v-card-actions>
          <v-btn color="grey" @click="$emit('update:modelValue', false)">Cancel</v-btn>
          <v-btn color="primary" @click="saveAccessory">Save</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </template>
  
  <script setup lang="ts">
  import { createAccessory, updateAccessory } from '@/lib/api';
import type { Accessory } from '@/lib/api/types';
import { useAppStore } from '@/stores/AppStore';
import { ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
const appStore = useAppStore();
const { t } = useI18n();

  const props = defineProps({
    modelValue: Boolean,
    accessory: Object
  });
  const emit = defineEmits(['update:modelValue', 'save']);
  const form = ref({ name: '', description: '', isActive: 'Y' } as Accessory);
  
  watch(() => props.accessory, (newVal:any) => {
    form.value = newVal ? { ...newVal } : { name: '', description: '', isActive: 'Y' };
  }, { immediate: true });
  
  const saveAccessory = async () => {
    appStore.startLoader( 'savingAccessory', t( 'app.auth.loader.savingAccessory' ) );

    if (form.value.accessoryId) {
      await updateAccessory(form.value);
    } else {
      await createAccessory(form.value);
    }
    appStore.stopLoader( 'savingAccessory');

    emit('update:modelValue', false);
    emit('save');
  };
  </script>