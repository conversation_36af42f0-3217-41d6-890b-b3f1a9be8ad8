<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { getAgreementByDeviceId } from '@/lib/api';
import { useAppStore } from '@/stores/AppStore';
import { useI18n } from 'vue-i18n';
import DARequestForm from './DARequestForm.vue';
import type { DeviceAgreement } from '@/lib/common/types';

const route = useRoute();
const appStore = useAppStore();
const { t } = useI18n();
const agreementData = ref<DeviceAgreement | null>(null);
const isLoading = ref(true);

// Get the device agreement ID from the route params
const deviceAgreementId = route.params.id as string;

// Fetch the agreement data when component mounts
onMounted(async () => {
  if (deviceAgreementId) {
    try {
      appStore.startLoader('fetchingAgreement', t('app.auth.loader.fetchingAgreement'));
      const response = await getAgreementByDeviceId(parseInt(deviceAgreementId));
      agreementData.value = response.data;
    } catch (error) {
      console.error('Error fetching agreement details:', error);
    } finally {
      appStore.stopLoader('fetchingAgreement');
      isLoading.value = false;
    }
  }
});
</script>

<template>
  <DARequestForm v-if="!isLoading" mode="edit" :agreement-data="agreementData" />
</template>