<script setup lang="ts">
	/**
	 * @file Sample Page: User data.
	 * @version 1.0.0
	 * @since 1.0.0
	 * <AUTHOR> <<EMAIL>>
	 * @license Copyright (c) 2022 Canon Canada Inc.
	 */

    /**
     * -------
     * Imports
     * -------
     */

    import type { MSGraphUser, EmpDemoUser } from '@/lib/common/types';
    import type { Ref } from 'vue';

    import { useAppStore } from '@/stores/AppStore';
    import { useUserStore } from '@/stores/UserStore';
    import { getUserByPrincipal, getUsersByName, getUserPhotoURL } from '@/lib/msGraph';
    import getAccessToken from '@/composables/auth/getAccessToken';
    import { MSGraphUserPhotoSize } from '@/lib/common/types';
    import { onMounted, ref } from 'vue';
    import moment from 'moment';

    /**
     * ----
     * Main
     * ----
     */

    // Add stores.
    const appStore = useAppStore();
    const userStore = useUserStore();

    let userTab = ref();
    let employeeSearch = ref();
    let employeeResults = ref();
    let userSearchLoader = ref(false);
    let foundUserDetails : Ref<MSGraphUser> | Ref<undefined> = ref();
    let foundUserPhoto = ref();
    let empDemoDetails : Ref<EmpDemoUser> | Ref<undefined> = ref();

    // Employee search debounce timer.
    let debounceTimer : any = null;

    // Searches for employee based on field value. Triggered when field changes.
    // This function uses a debounce method so that a user search is not triggered
    // every key press but rather when the user stops typing. This minimizes the
    // requests to the MS Graph.
    const performSearch = async ( value : any ) =>
    {
        employeeResults.value = undefined;

        // Don't search on no value or if the value is the name of the user already selected.
        if ( !value || value == employeeSearch?.value?.displayName )
        {
            return;
        }

        // Start loader for field.
        userSearchLoader.value = true;

        // Clear the timer to start the wait time over again.
        clearTimeout( debounceTimer );

        // Wait for 700ms before searching MS Graph (waits for user to stop typing).
        debounceTimer = setTimeout( async () =>
        {
            const accessToken = await getAccessToken();
            
            if ( accessToken )
            {
                // Call MS Graph
                const results : any = await getUsersByName( accessToken, value );

                // Update the auto complete field item list.
                if ( results )
                {
                    employeeResults.value = ( results as any ).value;
                }
            }

            userSearchLoader.value = false;
        }, 700);    
    }

    // Obtains the employee details when an employee is selected in the Auto Complete
    // field from the list of searched employees.
    const getUserDetails = async ( value : any ) =>
    {
        // No value
        if ( !value )
        {
            return;
        }

        const accessToken = await getAccessToken();

        if ( accessToken )
        {
            // Get specific user details.
            foundUserDetails.value = await getUserByPrincipal( accessToken, ( value as MSGraphUser ).userPrincipalName );
            foundUserPhoto.value = await getUserPhotoURL( accessToken, ( value as MSGraphUser ).userPrincipalName, MSGraphUserPhotoSize.SIZE_2  );

            // Get details from Employee Demographics.
            empDemoDetails = await getEmployeeById( foundUserDetails.value.employeeID );
        }
    }

    // Get's employee information by Canon ID.
    const getEmployeeById = async ( empID : string ) : Promise <any> =>
    {
        if ( !empID )
        {
            return null;
        }

        let user : EmpDemoUser | null = null;

        // Init headers object for Fetch API.
        const requestHeaders = new Headers();

        // Set auth details
        requestHeaders.set('Authorization', 'Basic ' + btoa( 'Emp_Adv' + ":" + 'wrNkt8Zt3v2ytscr' ) );

        // Init options for Fetch API
        const fetchOptions = 
        {
            method: 'GET',
            headers: requestHeaders
        };

        // Execute the API request.
        const response = await fetch( `https://brm-edws.cci.canon.info/emp_demo/api/emp_adv/id/${ empID }`, fetchOptions );

        if ( response )
        {
            const responseObj : any = await response.json();

            // Set properties for user.
            user =
            {
                employeeID: responseObj.employeeID,
                emailAddress: responseObj.email,
                givenName: responseObj.employeeGivenName,
                surName: responseObj.employeeLastName,
                displayName: responseObj.employeeOutlookName,
                jobTitle: responseObj.jobTitle,
                department: responseObj.departDescription1,
                companyName: responseObj.canonCompany,
                officeStreet: responseObj.officeAddressStreet,
                officeCity: responseObj.officeAddressCity,
                officeState: responseObj.officeAddressProvince,
                officePostalCode: responseObj.officeAddressPostal,
                phoneMobile: responseObj.officePhone,
                phoneFax: responseObj.officeFax,
                manager: responseObj.managerName,
                managerID: responseObj.managerID,
                companyCode: responseObj.canonCompanyCode,
                departmentDesc: responseObj.departDescription2,
                departmentID: responseObj.departID,
                directReports: responseObj.directReports,
                employeeGL: responseObj.employeeGL,
                employeeStatus: responseObj.employeeStatus,
                employeeType: responseObj.employeeType,
                flsaStatus: responseObj.flsaStatus,
                jobCode: responseObj.jobCode,
                jobStartDate: responseObj.canonHireDate,
                jobWorkWeekHours: responseObj.jobWorkWeekHours,
                lastUpdated: responseObj.lastUpdated,
                managerLevel: responseObj.managerLevel,
                officeExtension: responseObj.officeExt,
                officeLocation: responseObj.officeLocation,
                officeLocationCode: responseObj.officeLocationCode,
                successShareID: responseObj.successShareID,
                unionEmp: responseObj.unionEmp
            };
        }

        return user;
    }

    // Executes after Vue template is loaded to DOM. Required on all pages.
    // Extend the below functionality to add additional page initializations as needed.
    onMounted ( async () =>
    {
        // Required on -ALL- pages to stop page loader.
        appStore.stopPageLoader();
    });
</script>

<template>
	<v-container class="pt-8">
		<v-row class="text-center">
			<v-col cols="12">
				<span class="text-h3">User</span>
			</v-col>
		</v-row>

		<v-row class="text-left">
			<v-col cols="12">
                <v-sheet>
                    <p>After a user is logged in, the application template will automatically gather all of the available Azure user profile data for the user. This data is stored in the <b>User Store</b>. This page provides examples with how to retrieve and display/use that data.</p>

                    <br>
                    <p class="text-error" v-if="!userStore.authenticated">You are not logged in. Login to see your user details.</p>

                    <div v-if="userStore.authenticated">
                        <p class="text-h4">Current User Data</p>

                        <v-form>
                            <v-container>
                                <v-row v-if="userStore.photoURL">
                                    <v-col cols="12" md="12" class="d-flex justify-center my-5">
                                        <v-avatar rounded size="128" align="center">
                                            <v-img :src="userStore.photoURL"></v-img>
                                        </v-avatar>
                                    </v-col>
                                </v-row>
                                <v-row>
                                    <v-col cols="12" md="6">
                                        <v-text-field density="compact" variant="outlined" label="Microsoft ID" type="input" readonly :model-value="userStore.microsoftID" persistent-hint hint="Unique ID assigned from Azure AD."></v-text-field>
                                    </v-col>
                                    <v-col cols="12" md="6">
                                        <v-text-field density="compact" variant="outlined" label="Canon Employee ID" type="input" readonly :model-value="userStore.employeeID" persistent-hint hint="Unique employee ID assigned by HR."></v-text-field>
                                    </v-col>
                                    <v-col cols="12" md="6">
                                        <v-text-field density="compact" variant="outlined" label="Login Username" type="input" readonly :model-value="userStore.userPrincipalName" persistent-hint hint="Email address used to login to M365."></v-text-field>
                                    </v-col>
                                    <v-col cols="12" md="6">
                                        <v-text-field density="compact" variant="outlined" label="Email Address" type="input" readonly :model-value="userStore.emailAddress" persistent-hint hint="Primary Canon email address."></v-text-field>
                                    </v-col>
                                    <v-col cols="6" md="6">
                                        <v-text-field density="compact" variant="outlined" label="Given Name" type="input" readonly :model-value="userStore.givenName" persistent-hint hint="Also known as first name."></v-text-field>
                                    </v-col>
                                    <v-col cols="12" md="6">
                                        <v-text-field density="compact" variant="outlined" label="Surname" type="input" readonly :model-value="userStore.surName" persistent-hint hint="Also known as last name."></v-text-field>
                                    </v-col>
                                    <v-col cols="6" md="6">
                                        <v-text-field density="compact" variant="outlined" label="Display Name" type="input" readonly :model-value="userStore.displayName" persistent-hint hint="Full name displayed in M365 applications (preferred name)."></v-text-field>
                                    </v-col>
                                    <v-col cols="12" md="6">
                                        <v-text-field density="compact" variant="outlined" label="Job Title" type="input" readonly :model-value="userStore.jobTitle" persistent-hint hint="Current employee job title."></v-text-field>
                                    </v-col>
                                    <v-col cols="6" md="6">
                                        <v-text-field density="compact" variant="outlined" label="Department" type="input" readonly :model-value="userStore.department" persistent-hint hint="Current employee department."></v-text-field>
                                    </v-col>
                                    <v-col cols="12" md="6">
                                        <v-text-field density="compact" variant="outlined" label="Company Name" type="input" readonly :model-value="userStore.companyName" persistent-hint hint="Current Canon company which employees the employee."></v-text-field>
                                    </v-col>
                                    <v-col cols="12" md="6">
                                        <v-text-field density="compact" variant="outlined" label="Address: Street" type="input" readonly :model-value="userStore.officeStreet" persistent-hint hint="Street address of Canon Company the employee is based out of."></v-text-field>
                                    </v-col>
                                    <v-col cols="12" md="6">
                                        <v-text-field density="compact" variant="outlined" label="Address: Street" type="input" readonly :model-value="userStore.officeCity" persistent-hint hint="Street address of Canon Company the employee is based out of."></v-text-field>
                                    </v-col>
                                    <v-col cols="12" md="6">
                                        <v-text-field density="compact" variant="outlined" label="Address: State/Province" type="input" readonly :model-value="userStore.officeState" persistent-hint hint="State/province of Canon Company the employee is based out of."></v-text-field>
                                    </v-col>
                                    <v-col cols="12" md="6">
                                        <v-text-field density="compact" variant="outlined" label="Address: Postal/Zip Code" type="input" readonly :model-value="userStore.officePostalCode" persistent-hint hint="Postal/Zip code of Canon Company the employee is based out of."></v-text-field>
                                    </v-col>
                                    <v-col cols="12" md="6">
                                        <v-text-field density="compact" variant="outlined" label="Address: Country" type="input" readonly :model-value="userStore.officeCountry" persistent-hint hint="Country of Canon Company the employee is based out of."></v-text-field>
                                    </v-col>
                                    <v-col cols="12" md="6">
                                        <v-text-field density="compact" variant="outlined" label="Mobile" type="input" readonly :model-value="userStore.phoneMobile" persistent-hint hint="Mobile phone for employee (usually just extension)"></v-text-field>
                                    </v-col>
                                    <v-col cols="12" md="6">
                                        <v-text-field density="compact" variant="outlined" label="Fax" type="input" readonly :model-value="userStore.phoneFax" persistent-hint hint="Fax phone for employee (usually blank or main number)."></v-text-field>
                                    </v-col>
                                    <v-col v-if="userStore.businessPhones" v-for="(phone, index) in userStore.businessPhones" cols="12" md="6">
                                        <v-text-field density="compact" variant="outlined" :label="`Business Phone ${ index + 1 }`" type="input" readonly :model-value="phone" persistent-hint hint="Business phone value (could be many)."></v-text-field>
                                    </v-col>
                                    <v-col v-if="userStore.manager" cols="12" md="6">
                                        <v-text-field density="compact" variant="outlined" label="Manager" type="input" readonly :model-value="userStore.manager.displayName" persistent-hint hint="Manager of employee. This is an full user object with all details available."></v-text-field>
                                    </v-col>
                                </v-row>
                            </v-container>
                        </v-form>

                        <p class="text-h4">Requesting User Details</p><br>

                        <p>You can request user details via MS Graph so long as your application has the <b>User.Read.All</b> permission scope assigned to it.</p>

                        <v-form>
                            <v-container>
                                <v-row>
                                    <v-col cols="12" md="12">
                                        <v-autocomplete
                                            v-model="employeeSearch"
                                            item-title="displayName"
                                            item-value="id"
                                            density="compact" variant="outlined"
                                            hint="Enter employee name"
                                            :items="employeeResults"
                                            label="Search Employee"
                                            prepend-inner-icon="person"
                                            hide-no-data
                                            return-object
                                            auto-select-first
                                            :loading="userSearchLoader"
                                            @update:search="performSearch"
                                            @update:modelValue="getUserDetails"
                                        >
                                        </v-autocomplete>
                                    </v-col>
                                </v-row>
                            </v-container>
                        </v-form>

                        <v-card v-if="foundUserDetails">
                            <v-tabs bg-color="primary" fixed-tabs v-model="userTab">
                                <v-tab value="azure">
                                    Azure AD
                                </v-tab>
                                <v-tab value="demo">
                                    Employee Demographics
                                </v-tab>
                            </v-tabs>

                            <v-window v-model="userTab">
                                <v-window-item value="azure">
                                    <v-form class="mt-4">
                                        <v-container class="pt-0">
                                            <v-row v-if="foundUserPhoto">
                                                <v-col cols="12" md="12" class="d-flex justify-center mb-5">
                                                    <v-avatar rounded size="128" align="center">
                                                        <v-img :src="foundUserPhoto"></v-img>
                                                    </v-avatar>
                                                </v-col>
                                            </v-row>
                                            <v-row v-else>
                                                <v-col cols="12" md="12" class="d-flex justify-center mb-5">
                                                    <v-icon size="128">no_photography</v-icon>
                                                </v-col>
                                            </v-row>
                                            <v-row>
                                                <v-col cols="12" md="6">
                                                    <v-text-field density="compact" variant="outlined" label="Microsoft ID" type="input" readonly :model-value="foundUserDetails.microsoftID" persistent-hint hint="Unique ID assigned from Azure AD."></v-text-field>
                                                </v-col>
                                                <v-col cols="12" md="6">
                                                    <v-text-field density="compact" variant="outlined" label="Canon Employee ID" type="input" readonly :model-value="foundUserDetails.employeeID" persistent-hint hint="Unique employee ID assigned by HR."></v-text-field>
                                                </v-col>
                                                <v-col cols="12" md="6">
                                                    <v-text-field density="compact" variant="outlined" label="Login Username" type="input" readonly :model-value="foundUserDetails.userPrincipalName" persistent-hint hint="Email address used to login to M365."></v-text-field>
                                                </v-col>
                                                <v-col cols="12" md="6">
                                                    <v-text-field density="compact" variant="outlined" label="Email Address" type="input" readonly :model-value="foundUserDetails.emailAddress" persistent-hint hint="Primary Canon email address."></v-text-field>
                                                </v-col>
                                                <v-col cols="6" md="6">
                                                    <v-text-field density="compact" variant="outlined" label="Given Name" type="input" readonly :model-value="foundUserDetails.givenName" persistent-hint hint="Also known as first name."></v-text-field>
                                                </v-col>
                                                <v-col cols="12" md="6">
                                                    <v-text-field density="compact" variant="outlined" label="Surname" type="input" readonly :model-value="foundUserDetails.surName" persistent-hint hint="Also known as last name."></v-text-field>
                                                </v-col>
                                                <v-col cols="6" md="6">
                                                    <v-text-field density="compact" variant="outlined" label="Display Name" type="input" readonly :model-value="foundUserDetails.displayName" persistent-hint hint="Full name displayed in M365 applications (preferred name)."></v-text-field>
                                                </v-col>
                                                <v-col cols="12" md="6">
                                                    <v-text-field density="compact" variant="outlined" label="Job Title" type="input" readonly :model-value="foundUserDetails.jobTitle" persistent-hint hint="Current employee job title."></v-text-field>
                                                </v-col>
                                                <v-col cols="6" md="6">
                                                    <v-text-field density="compact" variant="outlined" label="Department" type="input" readonly :model-value="foundUserDetails.department" persistent-hint hint="Current employee department."></v-text-field>
                                                </v-col>
                                                <v-col cols="12" md="6">
                                                    <v-text-field density="compact" variant="outlined" label="Company Name" type="input" readonly :model-value="foundUserDetails.companyName" persistent-hint hint="Current Canon company which employees the employee."></v-text-field>
                                                </v-col>
                                                <v-col cols="12" md="6">
                                                    <v-text-field density="compact" variant="outlined" label="Address: Street" type="input" readonly :model-value="foundUserDetails.officeStreet" persistent-hint hint="Street address of Canon Company the employee is based out of."></v-text-field>
                                                </v-col>
                                                <v-col cols="12" md="6">
                                                    <v-text-field density="compact" variant="outlined" label="Address: City" type="input" readonly :model-value="foundUserDetails.officeCity" persistent-hint hint="Street address of Canon Company the employee is based out of."></v-text-field>
                                                </v-col>
                                                <v-col cols="12" md="6">
                                                    <v-text-field density="compact" variant="outlined" label="Address: State/Province" type="input" readonly :model-value="foundUserDetails.officeState" persistent-hint hint="State/province of Canon Company the employee is based out of."></v-text-field>
                                                </v-col>
                                                <v-col cols="12" md="6">
                                                    <v-text-field density="compact" variant="outlined" label="Address: Postal/Zip Code" type="input" readonly :model-value="foundUserDetails.officePostalCode" persistent-hint hint="Postal/Zip code of Canon Company the employee is based out of."></v-text-field>
                                                </v-col>
                                                <v-col cols="12" md="6">
                                                    <v-text-field density="compact" variant="outlined" label="Address: Country" type="input" readonly :model-value="foundUserDetails.officeCountry" persistent-hint hint="Country of Canon Company the employee is based out of."></v-text-field>
                                                </v-col>
                                                <v-col cols="12" md="6">
                                                    <v-text-field density="compact" variant="outlined" label="Mobile" type="input" readonly :model-value="foundUserDetails.phoneMobile" persistent-hint hint="Mobile phone for employee (usually just extension)"></v-text-field>
                                                </v-col>
                                                <v-col cols="12" md="6">
                                                    <v-text-field density="compact" variant="outlined" label="Fax" type="input" readonly :model-value="foundUserDetails.phoneFax" persistent-hint hint="Fax phone for employee (usually blank or main number)."></v-text-field>
                                                </v-col>
                                                <v-col v-if="foundUserDetails.businessPhones" v-for="(phone, index) in foundUserDetails.businessPhones" cols="12" md="6">
                                                    <v-text-field density="compact" variant="outlined" :label="`Business Phone ${ index + 1 }`" type="input" readonly :model-value="phone" persistent-hint hint="Business phone value (could be many)."></v-text-field>
                                                </v-col>
                                                <v-col v-if="foundUserDetails.manager" cols="12" md="6">
                                                    <v-text-field density="compact" variant="outlined" label="Manager" type="input" readonly :model-value="foundUserDetails.manager.displayName" persistent-hint hint="Manager of employee. This is an full user object with all details available."></v-text-field>
                                                </v-col>
                                            </v-row>
                                        </v-container>
                                    </v-form>
                                </v-window-item>
                        
                                <v-window-item value="demo">
                                    <v-form v-if="empDemoDetails" class="mt-4">
                                        <v-container class="pt-0">
                                            <v-row v-if="foundUserPhoto">
                                                <v-col cols="12" md="12" class="d-flex justify-center mb-5">
                                                    <v-avatar rounded size="128" align="center">
                                                        <v-img :src="foundUserPhoto"></v-img>
                                                    </v-avatar>
                                                </v-col>
                                            </v-row>
                                            <v-row v-else>
                                                <v-col cols="12" md="12" class="d-flex justify-center mb-5">
                                                    <v-icon size="128">no_photography</v-icon>
                                                </v-col>
                                            </v-row>
                                            <v-row>
                                                <v-col cols="12" md="6">
                                                    <v-text-field density="compact" variant="outlined" label="Canon Employee ID" type="input" readonly :model-value="empDemoDetails.employeeID" persistent-hint hint="Unique employee ID assigned by HR."></v-text-field>
                                                </v-col>
                                                <v-col cols="12" md="6">
                                                    <v-text-field density="compact" variant="outlined" label="Email Address" type="input" readonly :model-value="empDemoDetails.emailAddress" persistent-hint hint="Primary Canon email address."></v-text-field>
                                                </v-col>
                                                <v-col cols="6" md="6">
                                                    <v-text-field density="compact" variant="outlined" label="Given Name" type="input" readonly :model-value="empDemoDetails.givenName" persistent-hint hint="Also known as first name."></v-text-field>
                                                </v-col>
                                                <v-col cols="12" md="6">
                                                    <v-text-field density="compact" variant="outlined" label="Surname" type="input" readonly :model-value="empDemoDetails.surName" persistent-hint hint="Also known as last name."></v-text-field>
                                                </v-col>
                                                <v-col cols="6" md="6">
                                                    <v-text-field density="compact" variant="outlined" label="Display Name" type="input" readonly :model-value="empDemoDetails.displayName" persistent-hint hint="Full name displayed in M365 applications (preferred name)."></v-text-field>
                                                </v-col>
                                                <v-col cols="12" md="6">
                                                    <v-text-field density="compact" variant="outlined" label="Job Title" type="input" readonly :model-value="empDemoDetails.jobTitle" persistent-hint hint="Current employee job title."></v-text-field>
                                                </v-col>
                                                <v-col cols="12" md="6">
                                                    <v-text-field density="compact" variant="outlined" label="Job Code" type="input" readonly :model-value="empDemoDetails.jobCode" persistent-hint hint="Current job code of employee used by HR."></v-text-field>
                                                </v-col>
                                                <v-col cols="12" md="6">
                                                    <v-text-field density="compact" variant="outlined" label="Job Start Date" type="input" readonly :model-value="moment( empDemoDetails.jobStartDate )" persistent-hint hint="Date the employee started current job."></v-text-field>
                                                </v-col>
                                                <v-col cols="12" md="6">
                                                    <v-text-field density="compact" variant="outlined" label="Manager Level" type="input" readonly :model-value="empDemoDetails.managerLevel" persistent-hint hint="Special HR value indicating the employees rank in the company."></v-text-field>
                                                </v-col>
                                                <v-col cols="12" md="6">
                                                    <v-text-field density="compact" variant="outlined" label="Work Week Hours" type="input" readonly :model-value="empDemoDetails.jobWorkWeekHours" persistent-hint hint="Number of hours employee is designated to work per week."></v-text-field>
                                                </v-col>
                                                <v-col cols="6" md="6">
                                                    <v-text-field density="compact" variant="outlined" label="Department ID" type="input" readonly :model-value="empDemoDetails.departmentID" persistent-hint hint="ID of department used by HR."></v-text-field>
                                                </v-col>
                                                <v-col cols="6" md="6">
                                                    <v-text-field density="compact" variant="outlined" label="Department" type="input" readonly :model-value="empDemoDetails.department" persistent-hint hint="Current employee department."></v-text-field>
                                                </v-col>
                                                <v-col cols="6" md="6">
                                                    <v-text-field density="compact" variant="outlined" label="Department Desc." type="input" readonly :model-value="empDemoDetails.departmentDesc" persistent-hint hint="Secondary description of department."></v-text-field>
                                                </v-col>
                                                <v-col cols="12" md="6">
                                                    <v-text-field density="compact" variant="outlined" label="Company Name" type="input" readonly :model-value="empDemoDetails.companyName" persistent-hint hint="Current Canon company which employees the employee."></v-text-field>
                                                </v-col>
                                                <v-col cols="12" md="6">
                                                    <v-text-field density="compact" variant="outlined" label="Company Code" type="input" readonly :model-value="empDemoDetails.companyCode" persistent-hint hint="Short form code of Canon company."></v-text-field>
                                                </v-col>
                                                <v-col cols="12" md="6">
                                                    <v-text-field density="compact" variant="outlined" label="Office Location" type="input" readonly :model-value="empDemoDetails.officeLocation" persistent-hint hint="Canon office employee is assigned to."></v-text-field>
                                                </v-col>
                                                <v-col cols="12" md="6">
                                                    <v-text-field density="compact" variant="outlined" label="Office Location Code" type="input" readonly :model-value="empDemoDetails.officeLocationCode" persistent-hint hint="Canon office code employee is assigned to."></v-text-field>
                                                </v-col>
                                                <v-col cols="12" md="6">
                                                    <v-text-field density="compact" variant="outlined" label="Address: Street" type="input" readonly :model-value="empDemoDetails.officeStreet" persistent-hint hint="Street address of Canon Company the employee is based out of."></v-text-field>
                                                </v-col>
                                                <v-col cols="12" md="6">
                                                    <v-text-field density="compact" variant="outlined" label="Address: City" type="input" readonly :model-value="empDemoDetails.officeCity" persistent-hint hint="Street address of Canon Company the employee is based out of."></v-text-field>
                                                </v-col>
                                                <v-col cols="12" md="6">
                                                    <v-text-field density="compact" variant="outlined" label="Address: State/Province" type="input" readonly :model-value="empDemoDetails.officeState" persistent-hint hint="State/province of Canon Company the employee is based out of."></v-text-field>
                                                </v-col>
                                                <v-col cols="12" md="6">
                                                    <v-text-field density="compact" variant="outlined" label="Address: Postal/Zip Code" type="input" readonly :model-value="empDemoDetails.officePostalCode" persistent-hint hint="Postal/Zip code of Canon Company the employee is based out of."></v-text-field>
                                                </v-col>
                                                <v-col cols="12" md="6">
                                                    <v-text-field density="compact" variant="outlined" label="Mobile" type="input" readonly :model-value="empDemoDetails.phoneMobile" persistent-hint hint="Mobile phone for employee (usually just extension)"></v-text-field>
                                                </v-col>
                                                <v-col cols="12" md="6">
                                                    <v-text-field density="compact" variant="outlined" label="Extension" type="input" readonly :model-value="empDemoDetails.officeExtension" persistent-hint hint="Office extension."></v-text-field>
                                                </v-col>
                                                <v-col cols="12" md="6">
                                                    <v-text-field density="compact" variant="outlined" label="Fax" type="input" readonly :model-value="empDemoDetails.phoneFax" persistent-hint hint="Fax phone for employee (usually blank or main number)."></v-text-field>
                                                </v-col>
                                                <v-col cols="12" md="6">
                                                    <v-text-field density="compact" variant="outlined" label="Manager" type="input" readonly :model-value="empDemoDetails.manager" persistent-hint hint="Manager of employee. This is just a string."></v-text-field>
                                                </v-col>
                                                <v-col cols="12" md="6">
                                                    <v-text-field density="compact" variant="outlined" label="Manager ID" type="input" readonly :model-value="empDemoDetails.managerID" persistent-hint hint="ID of the manager."></v-text-field>
                                                </v-col>
                                                <v-col cols="12" md="6">
                                                    <v-text-field density="compact" variant="outlined" label="Direct Reports" type="input" readonly :model-value="empDemoDetails.directReports" persistent-hint hint="List of direct reports."></v-text-field>
                                                </v-col>
                                                <v-col cols="12" md="6">
                                                    <v-text-field density="compact" variant="outlined" label="GL Code" type="input" readonly :model-value="empDemoDetails.employeeGL" persistent-hint hint="Employee GL code."></v-text-field>
                                                </v-col>
                                                <v-col cols="12" md="6">
                                                    <v-text-field density="compact" variant="outlined" label="Employee Status" type="input" readonly :model-value="empDemoDetails.employeeStatus" persistent-hint hint="Active status of employee."></v-text-field>
                                                </v-col>
                                                <v-col cols="12" md="6">
                                                    <v-text-field density="compact" variant="outlined" label="Employee Type" type="input" readonly :model-value="empDemoDetails.employeeType" persistent-hint hint="Type of employee."></v-text-field>
                                                </v-col>
                                                <v-col cols="12" md="6">
                                                    <v-text-field density="compact" variant="outlined" label="FLSA Status" type="input" readonly :model-value="empDemoDetails.flsaStatus" persistent-hint hint="FLSA status of employee."></v-text-field>
                                                </v-col>
                                                <v-col cols="12" md="6">
                                                    <v-text-field density="compact" variant="outlined" label="Success Share ID" type="input" readonly :model-value="empDemoDetails.successShareID" persistent-hint hint="Success sharing ID."></v-text-field>
                                                </v-col>
                                                <v-col cols="12" md="6">
                                                    <v-text-field density="compact" variant="outlined" label="Union Flag" type="input" readonly :model-value="empDemoDetails.successShareID" persistent-hint hint="Special flag for union employees."></v-text-field>
                                                </v-col>
                                                <v-col cols="12" md="6">
                                                    <v-text-field density="compact" variant="outlined" label="Last Updated" type="input" readonly :model-value="moment( empDemoDetails.lastUpdated )" persistent-hint hint="Date the record was last updated."></v-text-field>
                                                </v-col>
                                            </v-row>
                                        </v-container>
                                    </v-form>
                                </v-window-item>
                            </v-window>
                        </v-card>
                    </div>
                </v-sheet>
			</v-col>
		</v-row>
	</v-container>
</template>

<style lang="scss"></style>