<template>
  <v-form ref="form" v-model="isFormValid" @submit.prevent="submitForm">
    <v-card>
      <v-card-title>
        {{ isCloneMode 
          ? t('legal.clone', { name: formData.legalAgreementName }) 
          : t(props.isEdit ? 'legal.edit' : 'legal.createNew') }}
      </v-card-title>
      <v-card-text>
        <v-container>
          <v-row>
            <v-col cols="12">
              <v-text-field
                v-model="formData.legalAgreementName"
                :label="t('legal.form.agreementName')"
                :rules="[v => !!v || t('legal.form.required')]"
                required
              ></v-text-field>
            </v-col>
            
            <v-col cols="12" md="6">
              <v-text-field
                v-model="formData.nameEnglish"
                :label="t('legal.form.nameEnglish')"
                :rules="[v => !!v || t('legal.form.required')]"
                required
              ></v-text-field>
            </v-col>
            
            <v-col cols="12" md="6">
              <v-text-field
                v-model="formData.nameFrench"
                :label="t('legal.form.nameFrench')"
                :rules="[v => !!v || t('legal.form.required')]"
                required
              ></v-text-field>
            </v-col>
            
            <v-col cols="12" md="6">
              <v-text-field
                v-model="formData.version"
                :label="t('legal.form.version')"
                :rules="[v => !!v || t('legal.form.required')]"
                required
                disabled
              ></v-text-field>
            </v-col>
            
            <v-col cols="12">
              <v-divider></v-divider>
              <p class="text-caption mt-2">{{ t('legal.form.contentOrUrl') }}</p>
            </v-col>
            
            <!-- <v-col cols="12" md="6">
              <v-text-field
                v-model="formData.urlEnglish"
                :label="t('legal.form.urlEnglish')"
              ></v-text-field>
            </v-col> -->
          
        <v-col cols="6" >
          <v-file-input density="compact"
            v-model="formData.fileEnglish" :rules="getFileRules"
            :label="t('legal.form.fileEnglish')" prepend-icon="attach_file"
            @change="handleFileUpload('ENGLISH')" />
 
        </v-col>
         <v-col cols="6" >
          <v-file-input density="compact"
            v-model="formData.fileFrench" :rules="getFileRules"
            :label="t('legal.form.fileFrench')" prepend-icon="attach_file"
            @change="handleFileUpload('FRENCH')" />
          <!-- <div v-if="props.formData.previewFileUrl" class="image-preview">
            <img :src="props.formData.previewFileUrl" alt="Image Preview" />
          </div> -->
        </v-col>
       
      
            
            <!-- <v-col cols="12" md="6">
              <v-text-field
                v-model="formData.urlFrench"
                :label="t('legal.form.urlFrench')"
              ></v-text-field>
            </v-col> -->
            
            <v-col cols="12" md="6">
              <v-textarea
                v-model="formData.contentEnglish"
                :label="t('legal.form.englishContent')"
                rows="5"
              ></v-textarea>
            </v-col>
            
            <v-col cols="12" md="6">
              <v-textarea
                v-model="formData.contentFrench"
                :label="t('legal.form.frenchContent')"
                rows="5"
              ></v-textarea>
            </v-col>
          </v-row>
        </v-container>
      </v-card-text>
      
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn color="grey" variant="text" @click="emit('cancel')">
          {{ t('legal.form.cancel') }}
        </v-btn>
        <v-btn 
          color="primary" 
          variant="text" 
          type="submit" 
          :disabled="!isFormValid || isSubmitting"
        >
          {{ isCloneMode 
            ? t('legal.form.createClone') 
            : t('legal.form.save') }}
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-form>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { useAppStore } from '@/stores/AppStore';
import { useSnackbarStore } from '@/stores/SnackbarStore';
import type { LegalAgreement, LegalVersion } from '@/lib/api/types';
import { useUserStore } from '@/stores/UserStore';
import { useRoute } from 'vue-router';
import { uploadFile } from '@/lib/api';

const { t } = useI18n();
const appStore = useAppStore();
const snackbar = useSnackbarStore();
const route = useRoute();
const getFileRules = [
  () =>  'File is required.'
];
// Determine if we're in clone mode
const isCloneMode = computed(() => route.name === 'cloneLegalAgreement');

const props = defineProps({
  legalAgreement: {
    type: Object as () => LegalAgreement | null,
    default: () => null
  },
  isEdit: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits<{
  (e: 'save', data: LegalAgreement): void;
  (e: 'clone', data: LegalAgreement): void;
  (e: 'cancel'): void;
}>();

const form = ref<any>(null);
const isFormValid = ref(false);
const isSubmitting = ref(false);

// Extended interface to include form-specific fields
interface LegalAgreementFormData extends LegalAgreement {
  // urlEnglish?: string;
  // urlFrench?: string;
  fileEnglish?: File| null;
  fileFrench?: File | null;
  fileNameEnglish?:string;
  fileNameFrench?:string
  contentEnglish?: string;
  contentFrench?: string;
  version?: number;
}

// Form data with default values
const formData = ref<LegalAgreementFormData>({
  legalId: props.legalAgreement?.legalId || 0,
  version: isCloneMode.value 
    ? (props.legalAgreement?.legalVersion || 0) + 1 
    : props.legalAgreement?.legalVersion || 1,
  legalAgreementName: props.legalAgreement?.legalAgreementName || '',
  nameEnglish: props.legalAgreement?.nameEnglish || '',
  nameFrench: props.legalAgreement?.nameFrench || '',
  urlEnglish: props.legalAgreement?.urlEn || '',
  urlFrench: props.legalAgreement?.urlFr || '',
  fileEnglish: null,
  fileFrench: null,
  fileNameEnglish:'',
  fileNameFrench:'',
  contentEnglish: isCloneMode.value ? '' : props.legalAgreement?.englishContent || '',
  contentFrench: isCloneMode.value ? '' : props.legalAgreement?.frenchContent || '',
  createdAt: props.legalAgreement?.createdAt || null,
  createdBy: props.legalAgreement?.createdBy || null,
  updatedAt: props.legalAgreement?.updatedAt || null,
  updatedBy: props.legalAgreement?.updatedBy || null,
  legalTypeId:props.legalAgreement?.legalTypeId||undefined
});

// Prepare payload for API
const preparePayload = (): LegalAgreement => {


  // Create the payload with the correct structure
  const payload: LegalAgreement = {
    version: formData.value.version || formData.value.version,
    legalAgreementName: formData.value.legalAgreementName,
    nameEnglish: formData.value.nameEnglish,
    nameFrench: formData.value.nameFrench,
    contentFrench: formData.value.contentFrench || undefined,
    contentEnglish: formData.value.contentEnglish || undefined,
    urlEnglish: formData.value.urlEnglish || undefined,
    urlFrench: formData.value.urlFrench || undefined,
    legalTypeId:formData.value.legalTypeId||undefined,
    microsoftAccountId:useUserStore().microsoftID|| undefined

 
  };
  
  return payload;
};
const alertMessage = ref('');
const showAlertBox = ref(false);
const maxFileSize = 5 * 1024 * 1024; // 5 MB in bytes

const handleFileUpload = async (type='ENGLISH') => {
  let file:File | null | undefined;
  if(type=='ENGLISH'){
  file = formData.value.fileEnglish;
  }
  else{
    file = formData.value.fileFrench;
  }
  showAlertBox.value = false;
  if (file) {
    // Validate file type
    const allowedTypes = ['application/pdf'];
    if (!allowedTypes.includes(file.type)) {
      alertMessage.value = t('component.BusinessCardForm.notavalidFilleError');
      showAlertBox.value = true;
 
      return;
    }

    // Validate file size
    if (file.size > maxFileSize) {
      alertMessage.value = t('component.BusinessCardForm.maxSizeError');
      showAlertBox.value = true;


      return;
    }

    // Create FormData and append the file
    const formDataUpload = new FormData();
    formDataUpload.append('file', file);

    // Call the API
    appStore.startLoader('ImageUpload', t('app.auth.loader.image_upload'));

    try {
      const response = await uploadFile(formDataUpload);
      appStore.stopLoader('ImageUpload');
      console.log(response)
      if(type=='ENGLISH'){
        formData.value.fileNameEnglish=response.data;
      }
      else{
        formData.value.fileNameFrench=response.data;
      }
      // props.formData.fileName = response.data;
      // const imageasBlobString = await downloadFile(response.data);
      // props.formData.previewFileUrl = imageasBlobString
    } catch (error) {
      console.log(error)

    }

  }
};
// Submit form handler
const submitForm = async () => {
  if (!isFormValid.value) return;
  
  try {
    isSubmitting.value = true;
    const payload = preparePayload();
    if(isCloneMode.value){
      emit('clone', preparePayload());
    }
    else{
      emit('save', payload);
    }
  } catch (error) {
    console.error('Error preparing legal agreement data:', error);
    snackbar.show({
      text: t('legal.messages.formError'),
      color: 'error',
      timeout: 5000
    });
  } finally {
    isSubmitting.value = false;
  }
};



</script>


