<template>
  <v-form ref="form" v-model="isFormValid" @submit.prevent="submitForm">
    <v-card>
      <v-card-title>
        {{ isCloneMode 
          ? t('legal.clone', { name: formData.legalAgreementName }) 
          : t(props.isEdit ? 'legal.edit' : 'legal.createNew') }}
      </v-card-title>
      <v-card-text>
        <v-container>
          <v-row>
            <v-col cols="12">
              <v-text-field
                v-model="formData.legalAgreementName"
                :label="t('legal.form.agreementName')"
                :rules="[v => !!v || t('legal.form.required')]"
                required
              ></v-text-field>
            </v-col>
            
            <v-col cols="12" md="6">
              <v-text-field
                v-model="formData.nameEnglish"
                :label="t('legal.form.nameEnglish')"
                :rules="[v => !!v || t('legal.form.required')]"
                required
              ></v-text-field>
            </v-col>
            
            <v-col cols="12" md="6">
              <v-text-field
                v-model="formData.nameFrench"
                :label="t('legal.form.nameFrench')"
                :rules="[v => !!v || t('legal.form.required')]"
                required
              ></v-text-field>
            </v-col>
            
            <v-col cols="12" md="6">
              <v-text-field
                v-model="formData.version"
                :label="t('legal.form.version')"
                :rules="[v => !!v || t('legal.form.required')]"
                required
                disabled
              ></v-text-field>
            </v-col>
            
            <v-col cols="12">
              <v-divider></v-divider>
              <p class="text-caption mt-2">{{ t('legal.form.contentOrUrl') }}</p>
            </v-col>
            
            <v-col cols="12" md="6">
              <v-text-field
                v-model="formData.urlEnglish"
                :label="t('legal.form.urlEnglish')"
              ></v-text-field>
            </v-col>
            
            <v-col cols="12" md="6">
              <v-text-field
                v-model="formData.urlFrench"
                :label="t('legal.form.urlFrench')"
              ></v-text-field>
            </v-col>
            
            <v-col cols="12" md="6">
              <v-textarea
                v-model="formData.contentEnglish"
                :label="t('legal.form.englishContent')"
                rows="5"
              ></v-textarea>
            </v-col>
            
            <v-col cols="12" md="6">
              <v-textarea
                v-model="formData.contentFrench"
                :label="t('legal.form.frenchContent')"
                rows="5"
              ></v-textarea>
            </v-col>
          </v-row>
        </v-container>
      </v-card-text>
      
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn color="grey" variant="text" @click="emit('cancel')">
          {{ t('legal.form.cancel') }}
        </v-btn>
        <v-btn 
          color="primary" 
          variant="text" 
          type="submit" 
          :disabled="!isFormValid || isSubmitting"
        >
          {{ isCloneMode 
            ? t('legal.form.createClone') 
            : t('legal.form.save') }}
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-form>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { useAppStore } from '@/stores/AppStore';
import { useSnackbarStore } from '@/stores/SnackbarStore';
import type { LegalAgreement, LegalVersion } from '@/lib/api/types';
import { useUserStore } from '@/stores/UserStore';
import { useRoute } from 'vue-router';

const { t } = useI18n();
const appStore = useAppStore();
const snackbar = useSnackbarStore();
const route = useRoute();

// Determine if we're in clone mode
const isCloneMode = computed(() => route.name === 'cloneLegalAgreement');

const props = defineProps({
  legalAgreement: {
    type: Object as () => LegalAgreement | null,
    default: () => null
  },
  isEdit: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits<{
  (e: 'save', data: LegalAgreement): void;
  (e: 'clone', data: LegalAgreement): void;
  (e: 'cancel'): void;
}>();

const form = ref<any>(null);
const isFormValid = ref(false);
const isSubmitting = ref(false);

// Extended interface to include form-specific fields
interface LegalAgreementFormData extends LegalAgreement {
  urlEnglish?: string;
  urlFrench?: string;
  contentEnglish?: string;
  contentFrench?: string;
  version?: number;
}

// Form data with default values
const formData = ref<LegalAgreementFormData>({
  legalId: props.legalAgreement?.legalId || 0,
  version: isCloneMode.value 
    ? (props.legalAgreement?.legalVersion || 0) + 1 
    : props.legalAgreement?.legalVersion || 1,
  legalAgreementName: props.legalAgreement?.legalAgreementName || '',
  nameEnglish: props.legalAgreement?.nameEnglish || '',
  nameFrench: props.legalAgreement?.nameFrench || '',
  urlEnglish: props.legalAgreement?.urlEn || '',
  urlFrench: props.legalAgreement?.urlFr || '',
    contentEnglish: isCloneMode.value ? '' : props.legalAgreement?.englishContent || '',
  contentFrench: isCloneMode.value ? '' : props.legalAgreement?.frenchContent || '',
  createdAt: props.legalAgreement?.createdAt || null,
  createdBy: props.legalAgreement?.createdBy || null,
  updatedAt: props.legalAgreement?.updatedAt || null,
  updatedBy: props.legalAgreement?.updatedBy || null,
  legalTypeId:props.legalAgreement?.legalTypeId||undefined
});

// Prepare payload for API
const preparePayload = (): LegalAgreement => {


  // Create the payload with the correct structure
  const payload: LegalAgreement = {
    version: formData.value.version || formData.value.version,
    legalAgreementName: formData.value.legalAgreementName,
    nameEnglish: formData.value.nameEnglish,
    nameFrench: formData.value.nameFrench,
    contentFrench: formData.value.contentFrench || undefined,
    contentEnglish: formData.value.contentEnglish || undefined,
    urlEnglish: formData.value.urlEnglish || undefined,
    urlFrench: formData.value.urlFrench || undefined,
    legalTypeId:formData.value.legalTypeId||undefined,
    microsoftAccountId:useUserStore().microsoftID|| undefined

 
  };
  
  return payload;
};

// Submit form handler
const submitForm = async () => {
  if (!isFormValid.value) return;
  
  try {
    isSubmitting.value = true;
    const payload = preparePayload();
    if(isCloneMode.value){
      emit('clone', preparePayload());
    }
    else{
      emit('save', payload);
    }
  } catch (error) {
    console.error('Error preparing legal agreement data:', error);
    snackbar.show({
      text: t('legal.messages.formError'),
      color: 'error',
      timeout: 5000
    });
  } finally {
    isSubmitting.value = false;
  }
};



</script>


