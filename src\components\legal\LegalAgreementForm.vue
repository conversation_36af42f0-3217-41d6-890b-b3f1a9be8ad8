<template>
  <v-form ref="form" v-model="isFormValid" @submit.prevent="submitForm">
    <v-card>
      <v-card-title>
        {{ isCloneMode 
          ? t('legal.clone', { name: formData.legalAgreementName }) 
          : t(props.isEdit ? 'legal.edit' : 'legal.createNew') }}
      </v-card-title>
      <v-card-text>
        <v-container>
          <v-row>
            <v-col cols="12">
              <v-text-field
                v-model="formData.legalAgreementName"
                :label="t('legal.form.agreementName')"
                :rules="[v => !!v || t('legal.form.required')]"
                required
              ></v-text-field>
            </v-col>
            
            <v-col cols="12" md="6">
              <v-text-field
                v-model="formData.nameEnglish"
                :label="t('legal.form.nameEnglish')"
                :rules="[v => !!v || t('legal.form.required')]"
                required
              ></v-text-field>
            </v-col>
            
            <v-col cols="12" md="6">
              <v-text-field
                v-model="formData.nameFrench"
                :label="t('legal.form.nameFrench')"
                :rules="[v => !!v || t('legal.form.required')]"
                required
              ></v-text-field>
            </v-col>
            
            <v-col cols="12" md="6">
              <v-text-field
                v-model="formData.version"
                :label="t('legal.form.version')"
                :rules="[v => !!v || t('legal.form.required')]"
                required
                disabled
              ></v-text-field>
            </v-col>
            
            <v-col cols="12">
              <v-divider></v-divider>
              <div class="mt-2 mb-3">
                <p class="text-subtitle-2 mb-1">{{ t('legal.form.contentOrFile') }}</p>
                <p class="text-caption text-medium-emphasis">
                  {{ t('legal.form.contentOrFileDescription') }}
                </p>
                <v-chip
                  v-if="isFileMode"
                  color="primary"
                  size="small"
                  class="mt-1"
                >
                  {{ t('legal.form.fileModeActive') }}
                </v-chip>
                <v-chip
                  v-else-if="isContentMode"
                  color="secondary"
                  size="small"
                  class="mt-1"
                >
                  {{ t('legal.form.contentModeActive') }}
                </v-chip>
              </div>
            </v-col>
            
            <!-- <v-col cols="12" md="6">
              <v-text-field
                v-model="formData.urlEnglish"
                :label="t('legal.form.urlEnglish')"
              ></v-text-field>
            </v-col> -->
          
        <v-col cols="6" >
          <!-- Show existing English file if available -->
          <div v-if="formData.urlEn" class="mb-2">
            <v-chip
              color="success"
              size="small"
              prepend-icon="description"
              class="mb-2 cursor-pointer"
              @click="openExistingFile('english')"
            >
              {{ t('legal.form.existingEnglishFile') }}
            </v-chip>
            <div class="text-caption text-medium-emphasis">
              {{ formData.fileNameEnglish || t('legal.form.existingFile') }}
            </div>
          </div>

          <v-file-input density="compact"
            v-model="formData.fileEnglish"
            :rules="getFileEnglishRules"
            :disabled="shouldDisableFiles"
            accept=".pdf"
            :label="formData.legalBlobEn ? t('legal.form.replaceEnglishFile') : t('legal.form.fileEnglish')"
            prepend-icon="attach_file"
            @change="handleFileUpload('ENGLISH')" />

        </v-col>
         <v-col cols="6" >
          <!-- Show existing French file if available -->
          <div v-if="formData.urlFr" class="mb-2">
            <v-chip
              color="success"
              size="small"
              prepend-icon="description"
              class="mb-2 cursor-pointer"
              @click="openExistingFile('french')"
            >
              {{ t('legal.form.existingFrenchFile') }}
            </v-chip>
            <div class="text-caption text-medium-emphasis">
              {{ formData.fileNameFrench || t('legal.form.existingFile') }}
            </div>
          </div>

          <v-file-input density="compact"
            v-model="formData.fileFrench"
            :rules="getFileFrenchRules"
            :disabled="shouldDisableFiles"
            accept=".pdf"
            :label="formData.legalBlobFr ? t('legal.form.replaceFrenchFile') : t('legal.form.fileFrench')"
            prepend-icon="attach_file"
            @change="handleFileUpload('FRENCH')" />

        </v-col>
       
            <v-col cols="12" md="6">
              <v-textarea
                v-model="formData.contentEnglish"
                :rules="getContentEnglishRules"
                :disabled="shouldDisableContent"
                :label="t('legal.form.englishContent')"
                rows="5"
                @input="handleContentChange"
              ></v-textarea>
            </v-col>

            <v-col cols="12" md="6">
              <v-textarea
                v-model="formData.contentFrench"
                :rules="getContentFrenchRules"
                :disabled="shouldDisableContent"
                :label="t('legal.form.frenchContent')"
                rows="5"
                @input="handleContentChange"
              ></v-textarea>
            </v-col>
          </v-row>
        </v-container>
      </v-card-text>
      
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn color="grey" variant="text" @click="emit('cancel')">
          {{ t('legal.form.cancel') }}
        </v-btn>
        <v-btn 
          color="primary" 
          variant="text" 
          type="submit" 
          :disabled="isSubmitting"
        >
          {{ isCloneMode 
            ? t('legal.form.createClone') 
            : t('legal.form.save') }}
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-form>
    <AlertBox
 :showAlertBox="showAlertBox" 
  :message="alertMessage"
  @close="showAlertBox = false"
/>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { useAppStore } from '@/stores/AppStore';
import { useSnackbarStore } from '@/stores/SnackbarStore';
import type { LegalAgreement } from '@/lib/api/types';
import { useUserStore } from '@/stores/UserStore';
import { useRoute } from 'vue-router';
import { getFileByUrl, uploadFile } from '@/lib/api';
import AlertBox from '../core/AlertBox.vue';
import { openBlobFile } from '@/lib/common/utils';

const { t } = useI18n();
const appStore = useAppStore();
const snackbar = useSnackbarStore();
const route = useRoute();
// Computed properties for validation logic
const hasAnyFile = computed(() => {
  return !!(formData.value.fileEnglish || formData.value.fileFrench);
});

const hasExistingFiles = computed(() => {
  return !!(formData.value.legalBlobEn || formData.value.legalBlobFr);
});

const hasAnyContent = computed(() => {
  return !!(formData.value.contentEnglish?.trim() || formData.value.contentFrench?.trim());
});

const isFileMode = computed(() => {
  return (hasAnyFile.value || hasExistingFiles.value) && !hasAnyContent.value;
});

const isContentMode = computed(() => {
  return hasAnyContent.value && !hasAnyFile.value && !hasExistingFiles.value;
});

const shouldDisableFiles = computed(() => {
  return hasAnyContent.value;
});

const shouldDisableContent = computed(() => {
  return hasAnyFile.value || hasExistingFiles.value;
});

// Validation rules
const getFileEnglishRules = computed(() => [
  (v: File | null) => {
    // If we have existing English file, no validation needed
    if (formData.value.legalBlobEn) {
      return true;
    }
    // If we're in file mode or starting with files, both files are required
    if (isFileMode.value || (hasAnyFile.value && !formData.value.fileFrench && !formData.value.legalBlobFr)) {
      return !!v || t('legal.form.fileEnglishRequired');
    }
    // If we have French file (new or existing), English file is also required
    if (formData.value.fileFrench || formData.value.legalBlobFr) {
      return !!v || t('legal.form.bothFilesRequired');
    }
    return true;
  }
]);

const getFileFrenchRules = computed(() => [
  (v: File | null) => {
    // If we have existing French file, no validation needed
    if (formData.value.legalBlobFr) {
      return true;
    }
    // If we're in file mode or starting with files, both files are required
    if (isFileMode.value || (hasAnyFile.value && !formData.value.fileEnglish && !formData.value.legalBlobEn)) {
      return !!v || t('legal.form.fileFrenchRequired');
    }
    // If we have English file (new or existing), French file is also required
    if (formData.value.fileEnglish || formData.value.legalBlobEn) {
      return !!v || t('legal.form.bothFilesRequired');
    }
    return true;
  }
]);

const getContentEnglishRules = computed(() => [
  (v: string) => {
    // If we're in content mode or starting with content, both contents are required
    if (isContentMode.value || (hasAnyContent.value && !formData.value.contentFrench?.trim())) {
      return !!(v && v.trim()) || t('legal.form.contentEnglishRequired');
    }
    // If we have French content, English content is also required
    if (formData.value.contentFrench?.trim()) {
      return !!(v && v.trim()) || t('legal.form.bothContentsRequired');
    }
    return true;
  }
]);

const getContentFrenchRules = computed(() => [
  (v: string) => {
    // If we're in content mode or starting with content, both contents are required
    if (isContentMode.value || (hasAnyContent.value && !formData.value.contentEnglish?.trim())) {
      return !!(v && v.trim()) || t('legal.form.contentFrenchRequired');
    }
    // If we have English content, French content is also required
    if (formData.value.contentEnglish?.trim()) {
      return !!(v && v.trim()) || t('legal.form.bothContentsRequired');
    }
    return true;
  }
]);

// Overall form validation
const isContentValid = computed(() => {
  // Must have either both files OR both contents, but not mixed or empty
  const hasBothNewFiles = !!(formData.value.fileEnglish && formData.value.fileFrench);
  const hasBothExistingFiles = !!(formData.value.legalBlobEn && formData.value.legalBlobFr);
  const hasMixedFiles = !!(
    (formData.value.fileEnglish || formData.value.legalBlobEn) &&
    (formData.value.fileFrench || formData.value.legalBlobFr)
  );
  const hasBothContents = !!(formData.value.contentEnglish?.trim() && formData.value.contentFrench?.trim());

  return hasBothNewFiles || hasBothExistingFiles || hasMixedFiles || hasBothContents;
});
// Determine if we're in clone mode
const isCloneMode = computed(() => route.name === 'cloneLegalAgreement');

const props = defineProps({
  legalAgreement: {
    type: Object as () => LegalAgreement | null,
    default: () => null
  },
  isEdit: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits<{
  (e: 'save', data: LegalAgreement): void;
  (e: 'clone', data: LegalAgreement): void;
  (e: 'cancel'): void;
}>();

const form = ref<any>(null);
const isFormValid = ref(false);
const isSubmitting = ref(false);

// Extended interface to include form-specific fields
interface LegalAgreementFormData extends LegalAgreement {
  // urlEnglish?: string;
  // urlFrench?: string;
  fileEnglish?: File| null;
  fileFrench?: File | null;
  fileNameEnglish?:string;
  fileNameFrench?:string
  contentEnglish?: string;
  contentFrench?: string;
  version?: number;
}

// Form data with default values
const formData = ref<LegalAgreementFormData>({
  legalId: props.legalAgreement?.legalId || 0,
  version: isCloneMode.value 
    ? (props.legalAgreement?.legalVersion || 0) + 1 
    : props.legalAgreement?.legalVersion || 1,
  legalAgreementName: props.legalAgreement?.legalAgreementName || '',
  nameEnglish: props.legalAgreement?.nameEnglish || '',
  nameFrench: props.legalAgreement?.nameFrench || '',
  fileEnglish: null,
  fileFrench: null,
  urlEn: isCloneMode.value? '' : props.legalAgreement?.urlEn,
  urlFr: isCloneMode.value? '' :props.legalAgreement?.urlFr,
  fileNameEnglish:'',
  fileNameFrench:'',
  contentEnglish: isCloneMode.value ? '' : props.legalAgreement?.englishContent || '',
  contentFrench: isCloneMode.value ? '' : props.legalAgreement?.frenchContent || '',
  createdAt: props.legalAgreement?.createdAt || null,
  createdBy: props.legalAgreement?.createdBy || null,
  updatedAt: props.legalAgreement?.updatedAt || null,
  updatedBy: props.legalAgreement?.updatedBy || null,
  legalTypeId:props.legalAgreement?.legalTypeId||undefined
});

// Prepare payload for API
const preparePayload = (): LegalAgreement => {


  // Create the payload with the correct structure
  const payload: LegalAgreement = {
    version: formData.value.version || formData.value.version,
    legalAgreementName: formData.value.legalAgreementName,
    nameEnglish: formData.value.nameEnglish,
    nameFrench: formData.value.nameFrench,
    contentFrench: formData.value.contentFrench || undefined,
    contentEnglish: formData.value.contentEnglish || undefined,
  fileNameEnglish:formData.value.fileNameEnglish,
  fileNameFrench:formData.value.fileNameFrench,
    legalTypeId:formData.value.legalTypeId||undefined,
    microsoftAccountId:useUserStore().microsoftID|| undefined

 
  };
  
  return payload;
};
const alertMessage = ref('');
const showAlertBox = ref(false);
const maxFileSize = 5 * 1024 * 1024; // 5 MB in bytes

// Helper function to clear content when files are uploaded
const clearContentFields = () => {
  formData.value.contentEnglish = '';
  formData.value.contentFrench = '';
};

// Helper function to clear files when content is entered
const clearFileFields = () => {
  formData.value.fileEnglish = null;
  formData.value.fileFrench = null;
  formData.value.fileNameEnglish = '';
  formData.value.fileNameFrench = '';
  // Also clear existing file blobs when switching to content mode
  formData.value.urlEn = undefined;
  formData.value.urlFr = undefined;
};

// Watch for content changes to clear files if needed
const handleContentChange = () => {
  if (hasAnyContent.value && hasAnyFile.value) {
    clearFileFields();
  }
};

// Function to open existing file in new tab
const openExistingFile = async (language: 'english' | 'french') => {
  const mapper={'english':'en','french':'fr'};
 try {
        appStore.startLoader('loadigFile', t('app.auth.loader.loadingFile'));

    const response = await getFileByUrl(mapper[language],formData.value.legalId);
        appStore.stopLoader('loadigFile');

    // Get the blob from response
const blob = new Blob([response.data], {
  type: 'application/pdf'
});
const url = URL.createObjectURL(blob);

//   if (filenameMatch) {
//     filename = filenameMatch[1];
//   }
// }
    const newWindow = window.open(url, '_blank');
  } catch (error) {
    console.error('Error opening file:', error);
    snackbar.show({
      text: t('legal.messages.fileOpenError'),
      color: 'error',
      timeout: 5000
    });
  }
 
};

const handleFileUpload = async (fileType='ENGLISH') => {
  let file:File | null | undefined;
  if(fileType=='ENGLISH'){
  file = formData.value.fileEnglish;
  }
  else{
    file = formData.value.fileFrench;
  }
  showAlertBox.value = false;
  if (file) {
    // Clear content fields when file is uploaded
    if (hasAnyContent.value) {
      clearContentFields();
    }
    // Validate file type
    const allowedTypes = ['application/pdf'];
    if (!allowedTypes.includes(file.type)) {
      alertMessage.value = t('legal.messages.notavalidFilleError');
      showAlertBox.value = true;
      if(fileType=='ENGLISH'){
        formData.value.fileEnglish = null;
      }
      else{
        formData.value.fileFrench = null;
      }
    
 
      return;
    }

    // Validate file size
    if (file.size > maxFileSize) {
      alertMessage.value = t('legal.messages.maxSizeError');
      showAlertBox.value = true;
    if(fileType=='ENGLISH'){
        formData.value.fileEnglish = null;
      }
      else{
        formData.value.fileFrench = null;
      }

      return;
    }

    // Create FormData and append the file
    const formDataUpload = new FormData();
    formDataUpload.append('file', file);

    // Call the API
    appStore.startLoader('ImageUpload', t('app.auth.loader.image_upload'));

    try {
      const response = await uploadFile(formDataUpload);
      appStore.stopLoader('ImageUpload');
      console.log(response)
      if(fileType=='ENGLISH'){
        formData.value.fileNameEnglish=response.data;
      }
      else{
        formData.value.fileNameFrench=response.data;
      }

    } catch (error: any) {
      appStore.stopLoader('ImageUpload');
      console.log(error);
     if(fileType=='ENGLISH'){
        formData.value.fileEnglish = null;
      }
      else{
        formData.value.fileFrench = null;
      }
      // Check for specific API error status
      if (error.response?.data?.errorMessage === 'LEGAL_DOC_SIZE_EXCEEDS_10_MB') {
        snackbar.show({
          text: t('legal.status.LEGAL_DOC_SIZE_EXCEEDS_10_MB'),
          color: 'error',
          timeout: 5000
        });
      } else if (error.response?.data?.status === 'LEGAL_DOC_INVALID_FILE_TYPE') {
        snackbar.show({
          text: t('legal.status.LEGAL_DOC_INVALID_FILE_TYPE'),
          color: 'error',
          timeout: 5000
        });
      } else {
        snackbar.show({
          text: t('legal.messages.formError'),
          color: 'error',
          timeout: 5000
        });
      }
    }

  }
};
// Submit form handler
const submitForm = async () => {
  if (!isFormValid.value) return;

  // Additional validation for content/file requirements
  if (!isContentValid.value) {
    snackbar.show({
      text: t('legal.messages.contentOrFileRequired'),
      color: 'error',
      timeout: 5000
    });
    return;
  }

  try {
    isSubmitting.value = true;
    const payload = preparePayload();
    if(isCloneMode.value){
      emit('clone', payload);
    }
    else{
      emit('save', payload);
    }
  } catch (error: any) {
    console.error('Error preparing legal agreement data:', error);

    // Check for specific API error status
    const errorStatus = error.response?.data?.status;
    let errorMessage = t('legal.messages.formError');

    if (errorStatus && t(`legal.status.${errorStatus}`) !== `legal.status.${errorStatus}`) {
      errorMessage = t(`legal.status.${errorStatus}`);
    }

    snackbar.show({
      text: errorMessage,
      color: 'error',
      timeout: 5000
    });
  } finally {
    isSubmitting.value = false;
  }
};



</script>

<style scoped>
.cursor-pointer {
  cursor: pointer;
  transition: transform 0.2s ease;
}

.cursor-pointer:hover {
  transform: scale(1.05);
}

.v-chip.cursor-pointer:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}
</style>

