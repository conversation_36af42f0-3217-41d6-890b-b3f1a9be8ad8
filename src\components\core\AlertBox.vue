<template>
    <v-dialog v-model="props.showAlertBox" max-width="400">
      <v-card>
        <v-card-title class="headline">
          {{ title }}
        </v-card-title>
        <v-card-text>
          {{ message }}
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="primary"  @click="closeAlert">{{ buttonText }}</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </template>
  
  <script setup lang="ts">
  import { ref, defineProps } from 'vue';
const emit = defineEmits(['close']);
  
  const props = defineProps({
    title: {
      type: String,
      default: 'Alert'
    },
    showAlertBox: {
      type: Boolean,
      default: false
    },
    message: {
      type: String,
      required: true
    },
    buttonText: {
      type: String,
      default: 'OK'
    }
  });
  
  const showAlert = ref(true);
  
  const closeAlert = () => {
    emit('close')
    showAlert.value = false;
  };
  </script>

  