export const openBlobFile = (blobData:string) => {
 

    // Convert base64 string to blob if needed
    let blob: Blob;
    if (typeof blobData === 'string') {
      // Assume it's a base64 string, convert to blob
      const byteCharacters = atob(blobData);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      blob = new Blob([byteArray], { type: 'application/pdf' });
    } else {
      // If it's already a blob
      blob = blobData as Blob;
    }

    // Create object URL and open in new tab
    const url = URL.createObjectURL(blob);
    const newWindow = window.open(url, '_blank');

 

    // Clean up the object URL after a delay to allow the browser to load it
    setTimeout(() => {
      URL.revokeObjectURL(url);
    }, 1000);

  
};