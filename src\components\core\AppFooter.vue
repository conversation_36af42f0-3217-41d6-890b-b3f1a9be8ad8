<script setup lang="ts">
    /**
	 * @file Footer component.
	 * @version 1.0.0
	 * @since 1.0.0
	 * <AUTHOR> <<EMAIL>>
	 * @license Copyright (c) 2024 Canon Canada Inc.
	 */

    /**
     * -------
     * Imports
     * -------
     */

    import { computed, onMounted, watch } from 'vue';
import { useI18n } from 'vue-i18n';

    /**
     * ----
     * Main
     * ----
     */

    const { t } = useI18n();
	const appTitle = computed(() => t('app.title'));
	const appVersion = import.meta.env.VITE_APP_VERSION;

    // Add language support.
 
</script>

<template>
    <v-footer color="footer" elevation="3" height="38" order="0" app>
        <v-row justify="center" no-gutters>
            <v-col class="py-4 text-center primary--text" cols="12">
				<span class="d-none d-md-inline-block">{{ appTitle }}&nbsp;&nbsp;<v-icon icon="horizontal_rule" size="10"></v-icon>&nbsp;&nbsp;</span>
                <strong>{{ t( 'component.appfooter.maintained' ) }}</strong>&nbsp;&nbsp;<v-icon icon="horizontal_rule" size="10"></v-icon>&nbsp;&nbsp;Version: {{ appVersion }}
            </v-col>
        </v-row>
    </v-footer>
</template>

<style lang="scss"></style>