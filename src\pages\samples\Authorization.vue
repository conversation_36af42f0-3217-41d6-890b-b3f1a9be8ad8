<script setup lang="ts">
	/**
	 * @file Sample Page: Page authentication.
	 * @version 1.0.0
	 * @since 1.0.0
	 * <AUTHOR> <<EMAIL>>
	 * @license Copyright (c) 2024 Canon Canada Inc.
	 */

    import { onMounted } from 'vue';
    import { useAppStore } from '@/stores/AppStore';
    import { useUserStore } from '@/stores/UserStore';
    import { CanonAuth } from '@/lib/canonAuth';

    // Add stores.
    const appStore = useAppStore();
    const userStore = useUserStore();

    // Executes after Vue template is loaded to DOM. Required on all pages.
    // Extend the below functionality to add additional page initializations as needed.
    onMounted ( () =>
    {
        // Required on -ALL- pages to stop page loader.
        appStore.stopPageLoader();
    });
</script>

<template>
	<v-container class="pt-8">
		<v-row class="text-center">
			<v-col cols="12">
				<span class="text-h3">Authorization</span>
			</v-col>
		</v-row>

		<v-row class="text-left">
			<v-col cols="12">
                <v-card class="pa-3">
                    <p>Routes are secured in Vue-Router <b>/src/router/index.ts</b>. This is done by adding a <b>meta</b> and <b>secure</b> tag to the route record. In addition, specific roles can be assigned to routes that limit access to certain users. Review the page links below as well as the router index declarations to best understand how to secure a route.</p>

                    <br/>

                    <p><b>Note</b>: The below examples only work for apps that have implemented the following roles: <b>User.Standard</b> and <b>User.Admin</b>.</p>
                    <br>
                    <v-divider></v-divider>

                    <br/>
                    <span class="text-h4">Standard Logged In Routes</span>
                    <br/><br/>

                    <p>Routes that are not specified with a role will just require a user to be logged in (authenticated). These routes will be accessible regardless of the users role.</p>

                    <v-btn color='primary' :to="{ name: 'pageAuthorizationNoRole' }" class="my-4">Secured Page - No Role Required</v-btn>

                    <br/><br/>

                    <p>Elements can be hidden based on the authentication state. This is handy if you have a public page that has elements that should only be visible to logged in users.</p>

                    <v-btn v-if="userStore.authenticated" color='primary' :to="{ name: 'pageAuthorizationNoRole' }" class="my-4">Secured Page - No Role Required - Visible to Logged In Users Only</v-btn>

                    <br/><br/>
                    <v-divider></v-divider>

                    <br/>
                    <span class="text-h4">Specific Role Routes</span>
                    <br/><br/>

                    <p>Routes that need to be gated behind certain user roles have the <b>role</b> property added to the <b>meta</b> object. The value of roles must be an array of strings that represent the roles that are accepted.</p>
                    <br>
                    <p><b>IMPORTANT</b>: By default, assigning multiple roles to a route means that a user must have every role to meet the requirements to access the route/page.</p>

                    <v-btn color='primary' :to="{ name: 'pageAuthorizationStandardRole' }" class="my-4">Secured Page - User.Standard Role Required</v-btn>

                    <br/><br/>

                    <p>Below is an example of a page protected by a role you will not have access to. This is what the user will experience if they click on a link that directs them to a route they do not have access to.</p>

                    <v-btn color='primary' :to="{ name: 'pageAuthorizationImpossibleRole' }" class="my-4">Secured Page - You Do Not Have Access - TRY ME!</v-btn>

                    <br/><br/>

                    <p>It's possible that users will access your app from external links to routes they do not have access to. The button below will open a new tab to simulate this.</p>

                    <v-btn target="_blank" color='primary' :to="{ name: 'pageAuthorizationImpossibleRole' }" class="my-4">Secured Page - You Do Not Have Access - External - TRY ME!</v-btn>

                    <br/><br/>

                    <p>Routes can have multiple roles assigned. By default, the user must have all the roles defined in the route to be able to able the access the page. Developers can specify the route checking logic using the <b>roleEnforcement</b> property setting it to the enum <b>RoleEnforcement.All</b> or <b>RoleEnforcement.Any</b>.</p>

                    <v-btn color='primary' :to="{ name: 'pageAuthorizationMultipleRolesAll' }" class="my-4">Secured Page - Multiple Roles - ALL</v-btn>
                    <br>
                    <v-btn color='primary' :to="{ name: 'pageAuthorizationMultipleRolesAny' }" class="my-4">Secured Page - Multiple Roles - ANY</v-btn>

                    <br/><br/>

                    <p><b>CanonAuth.hasAnyRoles()</b> or <b>CanonAuth.hasAllRoles()</b> can also be used to hide elements but these methods are not reactive.</p><br>

                    <p>The button below this text can only be viewed if you have the <b>User.Standard</b> role.</p>
                    <v-btn v-if="CanonAuth.hasAnyRoles(['User.Standard'])" color='primary' :to="{ name: 'pageAuthorizationMultipleRolesAll' }" class="my-4">Secured Page - Visible To Users With Role: User.Standard</v-btn>
                    <br>

                    <p>The button below this text can only be viewed if you have the <b>User.None.Existent.Role</b> role (should be hidden).</p>
                    <v-btn v-if="CanonAuth.hasAnyRoles(['User.None.Existent.Role'])" color='primary' :to="{ name: 'pageAuthorizationMultipleRolesAny' }" class="my-4">Secured Page - Visible To Users With Role: User.None.Existent.Role</v-btn>

                    <br>

                    <p>The button below this text can only be viewed if you have the both <b>User.Standard</b> and <b>User.Admin</b> roles.</p>
                    <v-btn v-if="CanonAuth.hasAllRoles(['User.Standard', 'User.Admin'])" color='primary' :to="{ name: 'pageAuthorizationMultipleRolesAny' }" class="my-4">Secured Page - Visible To Users With Role: User.Standard & User.Admin</v-btn>
                </v-card>
			</v-col>
		</v-row>
	</v-container>
</template>

<style lang="scss"></style>