<script setup lang="ts">
	/**
	 * @file User menu component.
	 * @version 1.0.0
	 * @since 1.0.0
	 * <AUTHOR> <<EMAIL>>
	 * @license Copyright (c) 2024 Canon Canada Inc.
	 */

    /**
     * -------
     * Imports
     * -------
     */

    import { ref, computed } from 'vue';
    import { useUserStore } from '@/stores/UserStore';
	import { useAppStore } from '@/stores/AppStore';
    import useUserLoginProcess from '@/composables/auth/userLoginProcess';
    import useUserLogoutProcess from '@/composables/auth/userLogoutProcess';
    import actionPermissions from '@/composables/auth/actionPermission';
    import { CN_Action } from '@/lib/common/types';
    import { useI18n } from 'vue-i18n';

    /**
     * ----
     * Main
     * ----
     */

    // Add stores.
    const userStore = useUserStore();
    const appStore = useAppStore();

    // Add language support.
    const { t } = useI18n({ useScope: 'global' })

    // Register composables
    const startLoginProcess = useUserLoginProcess;
    const startLogoutProcess = useUserLogoutProcess;

    // Data
    const userMenu = ref( false );

    // Init the theme control (on).
    let themeControl = true;

    // Update theme control based on environment variable.
    if ( import.meta.env.VITE_APP_CONTROL_THEME && import.meta.env.VITE_APP_CONTROL_THEME.toUpperCase() === 'FALSE' )
    {
        themeControl = false;
    }

    // Set the Vuetify theme.
    const setThemeDark = () =>
    {      
        appStore.setThemeDark( true );
    }

	// Set the Vuetify theme.
    const setThemeLight = () =>
    {      
        appStore.setThemeLight( true );
    }

    // Set the language preference to English.
    const setLanguageEnglish = () =>
    {      
        appStore.setLanguageEnglish();
    }

	// Set the language preference to French.
    const setLanguageFrench = () =>
    {      
        appStore.setLanguageFrench();
    }

    // Define login button click.
    const goLogin = async () =>
    {
        appStore.startLoader( 'Login', t( 'app.auth.loader.logging') );

        await startLoginProcess( t );

        appStore.stopLoader( 'Login' );
    }

    // Define logout button click.
    const goLogout = async () =>
    {
        appStore.startLoader( 'Logout', t( 'app.auth.loader.logout') );

        await startLogoutProcess();

        appStore.stopLoader( 'Logout' );
    }

    // Initial generation from name.
    const userInitials = computed( () =>
    {
        if ( userStore.givenName )
        {
            if ( userStore.surName )
            {
                return `${ userStore.givenName.slice( 0, 1 ) }${ userStore.surName.slice( 0, 1 ) }`;
            }
            else
            {
                return userStore.givenName.slice( 0, 1 );
            }
        }
        
        return null;
    });
</script>

<template>
    <v-btn v-if="!userStore.authenticated" @click="goLogin" variant="flat" color="primary" density="comfortable" class="mx-3">{{ t( 'component.usermenu.button.signin.label' ) }}</v-btn>

    <v-menu v-else v-model="userMenu" location="bottom" offset="16px" :close-on-content-click="false">
        <template v-slot:activator="{ props }">
            <v-btn v-bind="props" icon size="small" class="mx-3">
                <v-avatar v-if="userInitials" color="secondary" size="28">
                    <span style="font-size : 12px">{{ userInitials }}</span>
                </v-avatar>
                <v-icon v-else color="onHeader" size="24" icon="person"></v-icon>
            </v-btn>
        </template>

        <v-card class="pa-4">
            <v-btn icon="close" variant="text" density="compact" @click="userMenu = false" class="position-absolute right-0 top-0 mt-1 mr-1"></v-btn>

            <v-card-text class="pa-0">
                <div class="d-flex justify-center align-center flex-column pb-3 text-center">
                    <v-avatar v-if="userStore.photoURL" rounded size="80">
                        <v-img :src="userStore.photoURL" size="80" />
                    </v-avatar>
                    <v-avatar v-else rounded size="80">
                        <v-icon size="80" icon="person"></v-icon>
                    </v-avatar>

                    <span v-if="userStore.displayName" class="pt-2"><strong>{{ userStore.displayName }}</strong></span>
                    <span v-if="userStore.jobTitle">{{ userStore.jobTitle }}</span>
                </div>
            </v-card-text>

            <v-card-actions class="d-flex justify-center align-center flex-column pa-0">
                <v-row v-if="actionPermissions( CN_Action.PA_USER_UPDATE ) && themeControl" align="center" justify="center" class="mt-0" dense>
                    <v-col cols="12" align="center" justify="center" class="pa-0">
                        <v-btn @click="setThemeLight()" :active="appStore.currentTheme === 'appThemeLight'" :readonly="appStore.currentTheme === 'appThemeLight'" width="50%" variant="flat" prepend-icon="light_mode" density="compact" class="btn-right-smash mx-0">{{ t( 'component.usermenu.button.light.label' ) }}</v-btn>
                        <v-btn @click="setThemeDark()" :active="appStore.currentTheme === 'appThemeDark'" :readonly="appStore.currentTheme === 'appThemeDark'" width="50%" variant="flat" append-icon="dark_mode" density="compact" class="btn-left-smash mx-0">{{ t( 'component.usermenu.button.dark.label' ) }}</v-btn>
                    </v-col>
                </v-row>

                <v-row align="center" justify="center" dense class="pt-2">
                    <v-col class="pa-0" align="center" justify="center">
                        <v-btn @click="goLogout" width="100%" variant="text" color="red" size="small" density="comfortable">{{ t( 'component.usermenu.button.signout.label' ) }}</v-btn>
                    </v-col>
                </v-row>
            </v-card-actions>
        </v-card>
    </v-menu>
</template>

<style lang="scss">
    .userImageBorder
    {
        border-radius : 50%;
        border : 1px solid rgba(32,33,36,.13);
    }

    .btn-right-smash
    {
        border-left: 1px solid !important;
        border-top: 1px solid !important;
        border-bottom: 1px solid !important;
        border-top-right-radius: 0px;
        border-bottom-right-radius: 0px;
    }

    .btn-left-smash
    {
        border-right: 1px solid !important;
        border-top: 1px solid !important;
        border-bottom: 1px solid !important;
        border-top-left-radius: 0px;
        border-bottom-left-radius: 0px;
    }
</style>