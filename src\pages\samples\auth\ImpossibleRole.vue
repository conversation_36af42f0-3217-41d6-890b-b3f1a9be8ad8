<script setup lang="ts">
	/**
	 * @file Sample Page: Secure
	 * @version 1.0.0
	 * @since 1.0.0
	 * <AUTHOR> <<EMAIL>>
	 * @license Copyright (c) 2024 Canon Canada Inc.
	 */

    import { onMounted } from 'vue';
    import { useAppStore } from '@/stores/AppStore';

    // Add stores.
    const appStore = useAppStore();

    // Executes after Vue template is loaded to DOM. Required on all pages.
    // Extend the below functionality to add additional page initializations as needed.
    onMounted ( () =>
    {
        // Required on -ALL- pages to stop page loader.
        appStore.stopPageLoader();
    });
</script>

<template>
	<v-container class="pt-8">
		<v-row class="text-center">
			<v-col cols="12">
				<span class="text-h3">Authorization / Impossible Role</span>
			</v-col>
		</v-row>

		<v-row class="text-center">
			<v-col cols="12">
                <v-sheet>
                    <p>This route is not accessible because it is assigned a random (non-existent) role.</p>
                    <br>
                    <RouterLink :to="{ name: 'pageAuthorization' }">Go Back</RouterLink>
                </v-sheet>
			</v-col>
		</v-row>
	</v-container>
</template>

<style lang="scss"></style>