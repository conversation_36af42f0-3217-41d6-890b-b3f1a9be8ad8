<script setup lang="ts">
	/**
	 * @file Sample Page: Messages component.
	 * @version 1.0.0
	 * @since 1.0.0
	 * <AUTHOR> <<EMAIL>>
	 * @license Copyright (c) 2022 Canon Canada Inc.
	 */

    /**
     * -------
     * Imports
     * -------
     */

	import { useMessageStore } from "@/stores/MessageStore";
	import { MessageType } from "@/lib/common/types";
    import { useAppStore } from '@/stores/AppStore';
    import { onMounted } from 'vue';

    /**
     * ----
     * Main
     * ----
     */

    // Add stores.
    const appStore = useAppStore();
    const messageStore = useMessageStore();

    // Executes after Vue template is loaded to DOM. Required on all pages.
    // Extend the below functionality to add additional page initializations as needed.
    onMounted ( () =>
    {
        // Required on -ALL- pages to stop page loader.
        appStore.stopPageLoader();
    });

    // Examples
    const messageDefault = () =>
    {      
        messageStore.show
		({
            title: "Title: Default",
            body: "This message only has a title and body defined. You cannot close this message because the CLOSE button is disabled by default. Refresh the page to get out of this lock."
        });
    }

    const messageCentered = () =>
    {      
        messageStore.show
		({
            title: "Title: Centered Buttons",
            body: "This message is plain but the buttons are centered.",
            showContactIT : false,
			btnClose      : true,
            btnRefresh    : false,
            disableApp    : false,
            centerButtons : true
        });
    }

    const messageSuccess = () =>
    {      
        messageStore.show
		({
            type          : MessageType.SUCCESS,
            title         : "Title: Success",
            body          : "This is what a success message looks like. <strong>HTML</strong> is accepted here.",
            subtitle      : "Subtitle: Success",
            showContactIT : false,
			btnClose      : true,
            btnRefresh    : false,
            disableApp    : false
        });
    }

	const messageError = () =>
    {      
        messageStore.show
		({
            type          : MessageType.ERROR,
            title         : "Title: Error",
            body          : "This is what a error message looks like. <strong>HTML</strong> is accepted here.",
            subtitle      : "Subtitle: Error",
            showContactIT : false,
			btnClose      : true,
            btnRefresh    : false,
            disableApp    : false
        });
    }

	const messageInfo = () =>
    {      
		messageStore.show
		({
            type          : MessageType.INFO,
            title         : "Title: Information",
            body          : "This is what a information message looks like. <strong>HTML</strong> is accepted here.",
            subtitle      : "Subtitle: Information",
            showContactIT : false,
			btnClose      : true,
            btnRefresh    : false,
            disableApp    : false
        });
    }

	const messageWarning = () =>
    {      
		messageStore.show
		({
            type          : MessageType.WARNING,
            title         : "Title: Warning",
            body          : "This is what a warning message looks like. <strong>HTML</strong> is accepted here.",
            subtitle      : "Subtitle: Warning",
            showContactIT : false,
			btnClose      : true,
            btnRefresh    : false,
            disableApp    : false
        });
    }

	const messageRefresh = () =>
    {      
		messageStore.show
		({
            type          : MessageType.INFO,
			icon          : 'refresh',
            title         : "Title: Refresh",
            body          : "This is what a information message looks like with the refresh button enabled. The text immediately below is automatically generated. The refresh button will perform a hard reload of the page.",
            subtitle      : "Subtitle: Refresh",
            showContactIT : false,
			btnClose      : true,
            btnRefresh    : true,
            disableApp    : false
        });
    }

	const messageIT = () =>
    {      
		messageStore.show
		({
            type          : MessageType.INFO,
			icon          : 'help_center',
            title         : "Title: IT",
            body          : "This is what a information message looks like with the contact IT flag enabled. The text immediately below is automatically generated.",
            subtitle      : "Subtitle: IT",
            showContactIT : true,
			btnClose      : true,
            btnRefresh    : false,
            disableApp    : false
        });
    }

	const messageCustom1 = () =>
    {      
		messageStore.show
		({
            type          : MessageType.INFO,
			icon          : 'pan_tool',
            title         : "Title: Yes/No Question",
            body          : "This is a custom message window with yes and no buttons. These custom buttons can have call backs that perform individual tasks.",
            subtitle      : "Subtitle: Yes/No Question",
            showContactIT : false,
			btnCustom     :
			[
				{
					text     : 'Yes',
					colour   : 'success',
					callback : () => { alert('You clicked Yes!') },
					close    : false
				},
				{
					text     : 'No',
					colour   : 'red',
					callback : () => { alert('You clicked No! This message will now close.') },
					close    : true
				},
			],
			btnClose      : true,
            btnRefresh    : false,
            disableApp    : false
        });
    }

    const queuedMessages = () =>
    {
		messageStore.show
		({
            type          : MessageType.INFO,
            title         : "Title: Queued Message #1",
            body          : "This is the first of 3 messages that will appear.",
            subtitle      : "Subtitle: Message #1",
            showContactIT : false,
			btnClose      : true,
            btnRefresh    : false,
            disableApp    : false
        });

        messageStore.show
		({
            type          : MessageType.INFO,
            title         : "Title: Queued Message #2",
            body          : "This is the second of 3 messages that will appear.",
            subtitle      : "Subtitle: Message #2",
            showContactIT : false,
			btnClose      : true,
            btnRefresh    : false,
            disableApp    : false
        });

        messageStore.show
		({
            type          : MessageType.INFO,
            title         : "Title: Queued Message #3",
            body          : "This is the last of 3 messages that will appear.",
            subtitle      : "Subtitle: Message #3",
            showContactIT : false,
			btnClose      : true,
            btnRefresh    : false,
            disableApp    : false
        });
    }
</script>

<template>
	<v-container class="pt-8">
		<v-row class="text-center pb-5">
			<v-col cols="12">
				<span class="text-h3">Messages</span>
			</v-col>
		</v-row>

		<v-row>
			<v-col cols="12">
                <p>Displaying messages to users for various reasons is common place in any application. Because of this, the template includes a message component that standardizes the look and feel.</p>
                <br>
                <p>Message's can also be queued and operate on a first in / first out basis. This allows you to trigger multiple messages and know that they will appear as triggered to the user after they close them.</p>
                <br>
				<v-card width="100%">
					<v-card-title>Examples</v-card-title>

					<v-card-text>
                        <v-row class="text-center">
							<v-col cols="12" md="3">
								<v-btn @click="messageDefault()" color="primary" size="small" block>Default Message</v-btn>
							</v-col>
                            <v-col cols="12" md="3">
								<v-btn @click="messageCentered()" color="primary" size="small" block>Centered Buttons</v-btn>
							</v-col>
						</v-row>

						<v-row class="text-center">
							<v-col cols="12" md="3">
								<v-btn @click="messageSuccess()" color="success" size="small" block>Success Message</v-btn>
							</v-col>
							<v-col cols="12" md="3">
								<v-btn @click="messageError()" color="error" size="small" block>Error Message</v-btn>
							</v-col>
							<v-col cols="12" md="3">
								<v-btn @click="messageInfo()" color="info" size="small" block>Info Message</v-btn>
							</v-col>
							<v-col cols="12" md="3">
								<v-btn @click="messageWarning()" color="warning" size="small" block>Warning Message</v-btn>
							</v-col>
						</v-row>

						<v-row class="text-center">
							<v-col cols="12" md="3">
								<v-btn @click="messageRefresh()" color="info" size="small" block>Refresh Message</v-btn>
							</v-col>
							<v-col cols="12" md="3">
								<v-btn @click="messageIT()" color="info" size="small" block>IT Message</v-btn>
							</v-col>
							<v-col cols="12" md="3">
								<v-btn @click="messageCustom1()" color="info" size="small" block>Custom Buttons</v-btn>
							</v-col>
						</v-row>
					</v-card-text>
				</v-card>

                <br>

				<v-card width="100%">
					<v-card-title>Queued Example</v-card-title>

					<v-card-text>
						<v-row class="text-center">
							<v-col cols="12" md="3">
								<v-btn @click="queuedMessages()" color="primary" size="small" block>Queued Messages</v-btn>
							</v-col>
						</v-row>
					</v-card-text>
				</v-card>
			</v-col>
		</v-row>
	</v-container>
</template>

<style lang="scss"></style>