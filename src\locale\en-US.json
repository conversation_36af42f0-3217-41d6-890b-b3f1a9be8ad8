{"app": {"title": "Device Agreement", "auth": {"snack": {"success": "You have been successfully logged in.", "accessorydeleted": "Accessory deleted successfully", "accessorydeleteFailed": "Accessory deleted failed"}, "loader": {"logging": "Logging In ..", "logout": "Logging Out ..", "redirect": "Redirecting to Microsoft ..", "authenticating": "Authenticating ..", "locations": "Loading Locations", "fetch_agreements": "Fetching Agreements...", "image_upload": "Uploading Document...", "emailList": "Loading Emails...", "savingAccessory": "Saving Accessory...", "fetchingLegalDocs": "Fetching Legal Docs...", "checkingUserDetails": "Checking User Details...", "fetchingAssets": "Fetching Assets...", "fetchingAgreement": "Fetching Agreement...", "submittingAgreement": "Submitting agreement...", "savingDraft": "Saving draft...", "fetchingEventLog": "Fetching event log...", "acknowledgingAssets": "Acknowledging Assets...", "savingAgreement": "Saving Agreement...", "fetchingLegalAgreements": "Fetching Legal Agreements...", "fetchingLegalAgreement": "Fetching Legal Agreement...", "savingLegalAgreement": "Saving Legal Agreement...", "updatingLegalAgreement": "Updating Legal Agreement...", "deletingLegalAgreement": "Deleting Legal Agreement...", "fetchingVersions": "Fetching Versions...", "loadingFile": "Loading File...", "fetchingAccessories": "Fetching Accessories...", "reportingIssue": "Reporting issue..."}, "error": {"title_1": "Access Restricted", "title_2": "Not Signed In", "title_3": "No Access", "title_4": "Critical Authentication Error", "title_5": "Unknown Error", "title_6": "Login Expired", "title_7": "Account Disabled", "desc_1": "You do not have permission to access the requested content.", "desc_2": "You could not be signed in to the application.", "desc_3": "Your authentication request was successful, however, your account has not been granted permissions to access this application.<br><br>You have been automatically signed out.", "desc_4": "There was an unknown failure while attempting to log you in.", "desc_5": "There was a problem authenticating your login status. If you were previously logged in, you have been logged out.", "desc_6": "There was a problem authenticating your request. Your login session may have expired and have been logged out of this application. Sign-in and try again.", "desc_7": "Your account has been disabled and cannot be logged in.", "sub_1": "Role not assigned", "sub_2": "Authentication Check Failed", "sub_3": "Single Sign On", "warning": "Warning", "UNAUTHORIZED_ACCESS": "Unauthorized access. You do not have permission to perform this action.", "DA_UNDER_PROGRESS": "This agreement is currently being prepared", "LEGAL_DOC_SIZE_EXCEEDS_10_MB": "Legal document size exceeds the maximum limit of 10MB. Please upload a smaller file.", "LEGAL_DOC_INVALID_FILE_TYPE": "Invalid file type. Please upload a valid PDF document.", "LEGAL_MAPPED_WITH_DA": "This legal agreement is already mapped with a device agreement(s) and cannot be deleted.", "INVALID_LEGAL_ID": "Invalid legal agreement ID. The specified legal agreement does not exist."}, "button": {"go_home": {"label": "Go Home"}}}, "messages": {"agreementSaved": "Agreement saved successfully", "agreementUpdated": "Agreement updated successfully", "errorSubmittingForm": "Error submitting form. Please try again.", "agreementSubmitted": "Agreement submitted successfully", "agreementSaveFailed": "Failed to save agreement: {error}", "assetExists": "Asset with barcode {barcode} already exists", "restoreAssetSuccess": "Asset with barcode {barcode} restored successfully"}}, "page": {"404": {"header": "Page Not Found", "body": "The page you are looking for cannot be found."}, "home": {"user": {"header": "Welcome to the Device agreement App!", "desc": "You are now signed in."}, "anonymous": {"header": "Welcome to the Device agreement App!", "desc": "To begin, please sign-in.", "button": {"signin": {"label": "Sign In"}}}}}, "component": {"usermenu": {"button": {"signin": {"label": "Sign In"}, "signout": {"label": "Sign Out"}, "english": {"label": "English"}, "french": {"label": "French"}, "light": {"label": "Light"}, "dark": {"label": "Dark"}}}, "AgreementContent": {"title": "Service Agreement"}, "Accessories": {"name": "Name", "englishName": "English Name", "frenchName": "French Name", "action": "Action", "yes": "Yes", "no": "No", "addAccessory": "Add Accessory", "editAccessory": "Edit Accessory", "confirmDelete": "Are you sure you want to delete this Accessory?", "accessoryList": "Accessory List", "active": "Active", "inactive": "Inactive"}, "AccessoriesAdd": {"addTitle": "Add Accessory", "editTitle": "Edit Accessory", "nameLabel": "Name *", "englishAccessoryLabel": "English Name *", "frenchAccessoryLabel": "French Name *", "statusLabel": "Status", "saveButton": "Save", "cancelButton": "Cancel", "validation": {"nameRequired": "Name is required", "nameMinLength": "Name must be at least 2 characters", "nameMaxLength": "Name must be less than 50 characters", "descriptionMaxLength": "Description must be less than 200 characters"}}, "HelpDeskDialog": {"title": "Report an issue", "description": "Please describe the issue with your assigned items. We will review your request and action accordingly.", "textareaLabel": "Describe your issue", "textareaPlaceholder": "Please provide details about the problem you're encountering...", "submitButton": "Report", "cancelButton": "Cancel", "validation": {"required": "Please describe the issue", "minLength": "Please provide at least 10 characters", "maxLength": "Description must be less than 500 characters"}}, "DataTable": {"requestUniqueIdentifier": "Request Unique Identifier", "itemsPerPageText": "Rows per page"}, "DaList": {"createNew": "Create New DA", "pageTitle": "Device agreement list", "empName": "Employee Name", "status": "Status", "empCode": "Employee Code", "deviceAgreementNumber": "Device Agreement Number", "actions": "Actions"}, "appheader": {"button": {"home": {"label": "Home"}, "language": {"en": {"label": "English"}, "fr": {"label": "Français"}}, "accessories": {"label": "Accessories"}, "device_agreements": {"label": "Device agreements"}, "legal_agreements": {"label": "Legal agreements"}}}, "appsnackbar": {"button": {"close": {"label": "Close"}}}, "appmessage": {"refresh": "Please try clicking the <strong>Refresh</strong> button in your browser. If this problem continues, please contact I.T. support.", "support": "If this problem continues, please contact I.T. support.", "button": {"refresh": {"label": "Refresh"}, "close": {"label": "Close"}}}, "appfooter": {"maintained": "Maintained by CCI IT", "version": "Version"}, "ViewAgreement": {"acceptAgreement": "Agreement Acceptance", "acceptAgreementDescriptionDisabled": "Please confirm the assigned assets as well as review and acknowledge the assigned legal agreements", "acceptAgreementDescription": "By clicking 'I Accept', you confirm that you have reviewed all assigned assets and legal agreements and accept the terms and conditions.", "acceptAgreementDEscriptionOnBehalf": "By clicking  '{buttontext}', you confirm that you have reviewed all assigned assets and legal agreements and you accept the terms and conditions on behalf of {username}", "iAccept": "I Accept", "iAcceptFor": "Accept for {name}", "acceptingAgreement": "Accepting Agreement...", "agreementAcceptedSuccess": "Agreement accepted successfully", "agreementAcceptedFailure": "Failed to accept agreement", "agreementAcceptedFailure400": "Failed to accept agreement. Please ensure all items and legal agreements are acknowledged.", "acceptAgreementTitle": "Accept Agreement", "acceptAgreementConfirm": "Are you sure you want to accept this agreement? This action cannot be undone.", "accept": "Accept", "cancel": "Cancel", "acknowledgedAssets": "ACKNOWLEDGED ASSETS", "newlyAdded": "NEWLY ADDED", "previouslyRemoved": "PREVIOUSLY REMOVED", "category": "Category", "barcode": "Barcode", "serialNumber": "Serial Number", "description": "Description", "date": "Date", "agreementAcknowledgements": "Agreement Acknowledgements", "viewAgreement": "View Agreement", "acknowledge": "Acknowledge", "acknowledged": "Acknowledged", "pending": "Pending", "acceptanceStatement": "This agreement was accepted on {date} by {name}", "pendingAcceptance": "This agreement is pending acceptance", "reportIssue": "I see a problem with my <PERSON><PERSON> Listing!", "reportIssueTitle": "Report Issue", "reportIssueConfirm": "Are you sure you want to report an issue with your device listing?", "acknowledgeAssetsTitle": "Acknowledge Assets", "acknowledgeAssetsConfirm": "Are you sure you want to acknowledge that your device listing is correct?", "assetsAcknowledgedSuccess": "Assets acknowledged successfully", "assetsAcknowledgedFailure": "Failed to acknowledge assets", "reportingIssue": "Reporting issue...", "issueReportedSuccess": "Issue reported successfully", "issueReportedFailure": "Failed to report issue", "agreementDate": "Agreement Date:", "between": "Between:", "and": "- and -", "deviceListing": "Device Listing -", "checkEquipment": "Please check that the Products, Asset Tags, and Serial Numbers on the bottom of your equipment matches those listed below. Please review the legal agreement(s) assigned, acknowledge each and finalize the device agreement by clicking on the “I Accept” button.", "yes": "Yes", "no": "No", "acknowledgeAssets": "My device listing is correct", "acknowledgeLegalTitle": "Acknowledge Legal Agreement", "acknowledgeLegalConfirm": "Are you sure you want to acknowledge the '{agreement}' legal agreement?", "legalAcknowledgementFailed": "Failed to acknowledge legal agreement", "acknowledgingLegal": "Acknowledging legal agreement...", "unauthorized": "You are not authorized to view this agreement.", "Asset": "<PERSON><PERSON>", "Accessory": "Accessory"}}, "status": {"Draft": "Draft", "Approved": "Approved", "PendingAcceptence": "Waiting for Acknowledgement", "Deleted": "Deleted", "Accepted": "Accepted", "Terminated": "Terminated", "Completed": "Completed"}, "generic": {"deleteItem": "Delete Item", "cancel": "Cancel", "confirm": "Delete", "itemPerPage": "Items per page"}, "agreement": {"details": {"lastModifiedBy": "Last Modified By", "lastModified": "Last Modified", "employeeNotifyDate": "Employee Notify Date", "employeeAcceptanceDate": "Employee Acceptance Date", "employeeAcceptanceName": "Employee Acceptance Name", "currentLegalVersions": "Current Legal Versions", "version": "v{version}", "na": "N/A"}, "actions": {"viewagreement": "View Agreement", "terminate": "Terminate", "getlink": "Get Link", "sendreminder": "Send Reminder", "reactivate": "Reactivate", "teamschat": "Teams Reminder"}, "form": {"newAgreement": "NEW AGREEMENT", "editAgreement": "EDIT AGREEMENT", "employeeNameOrId": "Name or ID of the employee", "employeeDetails": {"name": "Name", "department": "Department", "id": "ID", "e-mail": "E-mail"}, "legal": "Legal", "selectLegalAgreements": "Select all legal agreements that apply.", "selectAtLeastOneLegalAgreement": "Please select at least one legal agreement.", "selectUser": "Please select an User first", "comment": "Comment", "wisetrack": "WISETRACK", "refreshAssets": "Refresh assets", "enterBarcode": "Enter barcode", "add": "Add", "noAssetsFound": "No Assets found in WiseTrack", "activeAssets": "Active Assets", "removedAssets": "Removed Assets", "barcode": "Barcode", "description": "Description", "serial": "Serial", "selectAccessory": "Select Accessory...", "selectedAccessory": "Selected Accessories", "saveAsDraft": "Save as Draft", "submit": "Submit", "next": "Next"}, "snacks": {"linkcopied": "Link copied to clipboard", "failedtocopy": "Failed to copy link", "reminderProcessed": "Agreement reminder processed successfully", "terminationProcessed": "Agreement termination processed successfully", "reactivationProcessed": "Agreement reactivation processed successfully", "reminderFailed": "Failed to process reminder", "terminationFailed": "Failed to process termination", "reactivationFailed": "Failed to process reactivation"}, "dialog": {"notice": "Notice", "existingAgreement": "A device agreement already exists for {name}. Do you want to view/edit that agreement?", "removeAsset": "Re<PERSON><PERSON>", "restoreAsset": "<PERSON><PERSON>", "confirmRemove": "Are you sure you want to remove this asset?", "confirmRestore": "Are you sure you want to restore this asset?", "remove": "Remove", "restore": "Rest<PERSON>", "cancel": "Cancel", "terminate": "Terminate", "terminateTitle": "Terminate Agreement", "terminateConfirm": "Are you sure you want to terminate this agreement?", "reminderTitle": "Send Reminder", "reminderConfirm": "Are you sure you want to send a reminder to the user?", "reactivate": "Reactivate", "send": "Send", "reactivateTitle": "Reactivate Agreement", "reactivateConfirm": "Are you sure you want to reactivate this agreement?", "yes": "Yes", "no": "No", "confirmAssetAssignment": "Employee {employeeName} found with barcode {barcodeInput}. Are you sure you want to assign this asset to {currentUserName}?", "removeAccessory": "Remove Accessory", "confirmRemoveAccessory": "Are you sure you want to remove this accessory?"}, "eventLog": {"title": "EVENT LOG", "noEvents": "No events found"}}, "legal": {"title": "Legal Agreements", "createNew": "Create New Legal Agreement", "edit": "Edit Legal Agreement", "clone": "Create <PERSON><PERSON> of {name}", "menu": {"edit": "Edit", "delete": "Delete", "cloneNewVersion": "Clone New Version", "currentVersion": "Current Version"}, "form": {"title": "Legal Agreement Form", "agreementName": "Agreement Name", "nameEnglish": "English Name", "nameFrench": "French Name", "version": "Version", "englishContent": "English Content", "frenchContent": "French Content", "urlEnglish": "English Document URL", "urlFrench": "French Document URL", "fileFrench": "French File", "fileEnglish": "English File", "contentOrUrl": "You can either provide content directly or specify a URL to the document", "contentOrFile": "Content or File Upload", "contentOrFileDescription": "Choose either file upload (both English and French files required) OR text content (both English and French content required). You cannot mix both methods.", "fileModeActive": "File Upload Mode", "contentModeActive": "Text Content Mode", "fileEnglishRequired": "English file is required when using file upload mode", "fileFrenchRequired": "French file is required when using file upload mode", "bothFilesRequired": "Both English and French files are required", "contentEnglishRequired": "English content is required when using text content mode", "contentFrenchRequired": "French content is required when using text content mode", "bothContentsRequired": "Both English and French content are required", "contentNotAllowedWithFiles": "Content text is not allowed when files are selected. Please use either files OR content, not both.", "existingEnglishFile": "Existing English File", "existingFrenchFile": "Existing French File", "existingFile": "Existing file available", "replaceEnglishFile": "Replace English File (Optional)", "replaceFrenchFile": "Replace French File (Optional)", "save": "Save", "createClone": "<PERSON><PERSON> <PERSON><PERSON>", "cancel": "Cancel", "required": "This field is required"}, "messages": {"createSuccess": "Legal agreement created successfully", "createError": "Failed to create legal agreement", "updateSuccess": "Legal agreement updated successfully", "updateError": "Failed to update legal agreement", "deleteSuccess": "Legal agreement deleted successfully", "deleteError": "Failed to delete legal agreement", "fetchError": "Failed to fetch legal agreements", "formError": "Error in form submission", "contentOrFileRequired": "Please provide either both files (English and French) OR both text contents (English and French). You cannot mix both methods or leave both empty.", "fileNotAvailable": "File is not available", "popupBlocked": "Popup was blocked. Please allow popups for this site to view the file.", "fileOpenError": "Error opening file. Please try again.", "notavalidFilleError": "Not a valid file. Please upload a PDF file.", "maxSizeError": "File size exceeds the limit of 5MB. Please upload a smaller file."}, "status": {"UNAUTHORIZED_ACCESS": "Unauthorized access. You do not have permission to perform this action.", "DA_UNDER_PROGRESS": "Device agreement is currently under progress and cannot be modified.", "LEGAL_DOC_SIZE_EXCEEDS_10_MB": "Legal document size exceeds the maximum limit of 10MB. Please upload a smaller file.", "LEGAL_DOC_INVALID_FILE_TYPE": "Invalid file type. Please upload a valid PDF document.", "LEGAL_MAPPED_WITH_DA": "This legal agreement is already mapped with a device agreement and cannot be deleted.", "INVALID_LEGAL_ID": "Invalid legal agreement ID. The specified legal agreement does not exist."}, "dialog": {"deleteTitle": "Confirm Delete", "deleteMessage": "Are you sure you want to delete this legal agreement? This action cannot be undone.", "delete": "Delete", "cancel": "Cancel", "viewContent": "Full Content", "close": "Close"}, "table": {"version": "Version", "id": "Legal ID", "versionId": "Version ID", "agreementName": "Agreement Name", "nameEnglish": "English Name", "nameFrench": "French Name", "createdAt": "Created At", "actions": "Actions", "englishDocument": "English Document", "frenchDocument": "French Document", "olderVersions": "Older Versions", "noOlderVersions": "No older versions available"}}, "tableFilter": {"searchPlaceholder": "Search", "filters": "Filters", "selectColumn": "Select Column", "selectFilter": "Select Filter", "value": "Value", "status": "Status", "clearAll": "Clear All", "apply": "Apply", "activeFilters": "Active Filters ({count})", "filterDescription": "{column}: {type}: {value}", "createNew": "Create New", "selectFiltrtType": "Select Filter Type", "contains": "Contains", "equals_to": "Equals To", "not_equals_to": "Not Equals To", "validation": {"valueRequired": "Value is required", "selectFilterType": "Select Filter Type", "selectColumnForFilter": "Select Column for Filter"}}}