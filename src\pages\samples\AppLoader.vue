<script setup lang="ts">
	/**
	 * @file Sample Page: App Loader component.
	 * @version 1.0.0
	 * @since 1.0.0
	 * <AUTHOR> <<EMAIL>>
	 * @license Copyright (c) 2024 Canon Canada Inc.
	 */

    import { onMounted } from 'vue';
    import { useAppStore } from '@/stores/AppStore';

    // Add stores.
    const appStore = useAppStore();

    // Example 1
    const example1 = () =>
    {
        appStore.startLoader( 'example1' );

        setTimeout( () =>
        {
            appStore.stopLoader( 'example1' );
        }, 3000)
    }

    // Example 2
    const example2 = () =>
    {
        appStore.startLoader( 'example2', 'Doing Something, Hold On!' );

        setTimeout( () =>
        {
            appStore.stopLoader( 'example2' );
        }, 3000)
    }

    // Example 3
    const example3 = () =>
    {
        appStore.startLoader( 'example3a', 'Working on Task 1 ..' );

        setTimeout( () =>
        {
            appStore.startLoader( 'example3b', 'Working on Task 2 ..' );
        }, 2000);

        setTimeout( () =>
        {
            appStore.stopLoader( 'example3b' );
        }, 4000);

        setTimeout( () =>
        {
            appStore.stopLoader( 'example3a' );
        }, 6000);
    }

    // Example 3
    const example4 = () =>
    {
        appStore.startLoader( 'example3a', 'Submitting Request ..' );

        setTimeout( () =>
        {
            appStore.stopLoader( 'example3a' );
            appStore.startLoader( 'example3b', 'Starting Approval Workflow ..' );
        }, 3000);

        setTimeout( () =>
        {
            appStore.stopLoader( 'example3b' );
            appStore.startLoader( 'example3c', 'Sending Email Notifications ..' );
        }, 6000);

        setTimeout( () =>
        {
            appStore.stopLoader( 'example3c' );
        }, 9000);
    }

    // Executes after Vue template is loaded to DOM. Required on all pages.
    // Extend the below functionality to add additional page initializations as needed.
    onMounted ( () =>
    {
        // Required on -ALL- pages to stop page loader.
        appStore.stopPageLoader();
    });
</script>

<template>
	<v-container class="pt-8">
		<v-row class="text-center">
			<v-col cols="12">
				<span class="text-h3">App Loader</span>
			</v-col>
		</v-row>

		<v-row class="text-left">
			<v-col cols="12">
				<p>The app loader component allows you to show a loading messages while performing tasks that need time to process such as calling an API to send or receive data.</p>
                <br>
                <p>The app loader is triggered via the <b>appStore</b> method <b>startLoader ( uid : string, text? : string )</b>. A unique ID is required and is used to distinguish between multiple loading processes that could be going on at the same time. Using the <b>startLoader()</b> method adds a loading item to the queue and it is only removed when the <b>stopLoader()</b> method is called with the same unique ID. In addition, a message can be added as a second parameter to provide context for the loading message to the user.</p>
                <br>
                <p>The app loader locks down the application from being accessed to prevent the user from making any changes until the process id done.</p>
                <br>
                <v-divider></v-divider>
                <br>
                <span class="text-h4">Examples</span><br><br>

                <p>1. Single item in the app loader queue (3 seconds) with no message.</p><br>
                <v-btn color="primary" @click="example1">Example 1 Loader</v-btn>

                <br><br>

                <p>2. Single item in the app loader queue (3 seconds) with message.</p><br>
                <v-btn color="primary" @click="example2">Example 2 Loader</v-btn>

                <br><br>

                <p>3. Multiple loaders triggered. Task 1 lasts for 6 seconds and Task 2 starts 2 seconds into Task 1 and lasts for 2 seconds. This functionality allows you to start and stop loading messages according to the tasks you are performing without having to worry about managing the loading messages. Just ensure the start and stop with the correct ID's and the loader will handle everything for you.</p><br>
                <v-btn color="primary" @click="example3">Example 3 Loader</v-btn>

                <br><br>

                <p>4. Multiple loaders triggered sequentially. This example shows what it would look like to start and stop loaders based on each task being finished one after another.</p><br>
                <v-btn color="primary" @click="example4">Example 4 Loader</v-btn>

                <br><br>

                <p><b>Note</b>: You can use the <b>stopLoader()</b> method without any ID's to force all loaders to end and clear the queue.</p>
			</v-col>
		</v-row>
	</v-container>
</template>

<style lang="scss"></style>