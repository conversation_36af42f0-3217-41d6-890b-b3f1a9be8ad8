<script setup lang="ts">
import { ref, onMounted, watch, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { agreementEventLog } from '@/lib/api';
import { useAppStore } from '@/stores/AppStore';

const props = defineProps({
  deviceAgreementId: {
    type: [Number, String],
    required: true
  },
  expanded: {
    type: Boolean,
    default: false
  }
});

const { t } = useI18n();
const appStore = useAppStore();
const eventLogs = ref<any[]>([]);
const loading = ref(false);
const expandedPanel = ref(props.expanded ? 0 : -1);

// Update expandedPanel when props.expanded changes
watch(() => props.expanded, (newVal) => {
  expandedPanel.value = newVal ? 0 : -1;
});

const fetchEventLogs = async () => {
  if (!props.deviceAgreementId) return;
  
  try {
    loading.value = true;
    appStore.startLoader('fetchingEventLog', t('app.auth.loader.fetchingEventLog'));
    
    const response = await agreementEventLog(props.deviceAgreementId.toString());
    eventLogs.value = response.data || [];
  } catch (error) {
    console.error('Error fetching event logs:', error);
  } finally {
    loading.value = false;
    appStore.stopLoader('fetchingEventLog');
  }
};

const formatDate = (dateString: string) => {
  if (!dateString) return '';
  
  const date = new Date(dateString);
  
  // Format: 'December 7, 2021 at 9:46 AM'
  return date.toLocaleDateString('en-US', { 
    month: 'long', 
    day: 'numeric', 
    year: 'numeric' 
  }) + ' at ' + date.toLocaleTimeString('en-US', { 
    hour: 'numeric', 
    minute: '2-digit', 
    hour12: true 
  });
};

watch(() => props.deviceAgreementId, (newVal) => {
  if (newVal) {
    fetchEventLogs();
  }
});

onMounted(() => {
  if (props.deviceAgreementId) {
    fetchEventLogs();
  }
});
</script>

<template>
  <v-card flat class="mt-4 event-log-container">
    <v-expansion-panels v-model="expandedPanel">
      <v-expansion-panel>
        <v-expansion-panel-title >
          <div class="d-flex align-center">
            <v-icon class="mr-2">history</v-icon>
            <span class="text-uppercase font-weight-medium">{{ t('agreement.eventLog.title') || 'EVENT LOG' }}</span>
          </div>
        </v-expansion-panel-title>
        
        <v-expansion-panel-text>
          <v-progress-linear v-if="loading" indeterminate></v-progress-linear>
          
          <div v-else-if="eventLogs.length === 0" class="text-center pa-4">
            {{ t('agreement.eventLog.noEvents') || 'No events found' }}
          </div>
          
          <v-timeline v-else density="compact" align="start">
            <v-timeline-item
              v-for="(event, index) in eventLogs"
              :key="index"
              dot-color="primary"
              size="small"
            >
              <div  style="width: 100%; margin-top:-10px">
                <div class="font-weight-medium" >{{ event.createdBy }}</div>
                <div class="text-caption text-grey" >{{ formatDate(event.createdAt) }}</div>
                <div class="text-caption text-grey" >{{ event.info }}</div>
              </div>
            </v-timeline-item>
          </v-timeline>
        </v-expansion-panel-text>
      </v-expansion-panel>
    </v-expansion-panels>
  </v-card>
</template>

<style scoped>
.event-log-container {
  border-radius: 4px;
  overflow: hidden;
margin:-14px}
</style>




