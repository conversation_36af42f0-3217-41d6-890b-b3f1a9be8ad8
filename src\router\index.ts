/**
 * @file Router configuration for application.
 * @version 1.0.0
 * @since 1.0.0
 * <AUTHOR> <<EMAIL>>
 * @license Copyright (c) 2024 Canon Canada Inc.
 */

/**
 * -------
 * Imports
 * -------
 */

import { createRouter, createWebHistory } from 'vue-router';
import { CanonAuthGuard } from '@/lib/canonAuth';
import { useAppStore } from '@/stores/AppStore';
import { RoleEnforcement } from '@/lib/common/types';
import DARequestForm from '../pages/DARequestForm.vue';
import AgreementContent from '../pages/AgreementContent.vue';

/**
 * ----
 * Main
 * ----
 */

// Defined routes for vue-router.
const routes  =
[
	{
		path: '/',
		name: 'viewMain',
		component: () => import( "@/views/MainView.vue" ),
		meta:
		{
			secure: false
		},
		children :
		[
			{
				path: '',
				name: 'pageHome',
				component: () => import( "@/pages/Home.vue" ),
				meta:
				{
					secure: false,
					roles:['User.Standard','User.Admin'],
					roleEnforcement:RoleEnforcement.ANY
				}
			},
            {
				path: '/tbshoot',
				name: 'pageTroubleshoot',
				component: () => import( "@/pages/Troubleshoot.vue" ),
				meta:
				{
					secure: true
				}
			},
			{
				path: 'accessories',
				name: 'accessories',
				component: () => import( "@/pages/AccessoriesList.vue" ),
				meta: {
				  secure: true,
				  roles:['User.Admin'],
				  roleEnforcement:RoleEnforcement.ANY
				},
			  },
			  {
				path: 'legal-agreements',
				name: 'legalAgreements',
				component: () => import("@/pages/LegalDocs.vue"),
				meta: {
				  secure: true,
				  roles: ['User.Admin'],
				  roleEnforcement: RoleEnforcement.ANY
				},
			  },
			  {
				path: 'legal-agreements/create',
				name: 'createLegalAgreement',
				component: () => import("@/pages/LegalAgreementCreate.vue"),
				meta: {
				  secure: true,
				  roles: ['User.Admin'],
				  roleEnforcement: RoleEnforcement.ANY
				},
			  },
			  {
				path: 'legal-agreements/edit/:id',
				name: 'editLegalAgreement',
				component: () => import("@/pages/LegalAgreementEdit.vue"),
				meta: {
				  secure: true,
				  roles:['User.Admin'],
				  roleEnforcement:RoleEnforcement.ANY
				},
			  },
			  	  {
				path: 'legal-agreements/clone/:id',
				name: 'cloneLegalAgreement',
				component: () => import("@/pages/LegalAgreementEdit.vue"),
				meta: {
				  secure: true,
				  roles:['User.Admin'],
				  roleEnforcement:RoleEnforcement.ANY
				},
			  },
			  {
				path: 'serviceAgreement',
				name: 'agreementContent',
				component: AgreementContent,
				meta: {
				  secure: true,
				  roles: ['User.Standard', 'User.Admin'],
				  roleEnforcement: RoleEnforcement.ANY
				}
			  },
			  {
				path: 'agreements',
				name: 'daList',
				component: () => import( "@/views/DARouter.vue" ),
				meta: {
				  secure: true,
				  roles:['User.Admin'],
				  roleEnforcement:RoleEnforcement.ANY
				},
				children:[
					{
						path: 'list',
						name: 'daList',
						component: () => import( "@/pages/DAList.vue" ),
						meta: {
						  secure: true,
						  roles:['User.Standard','User.Admin'],
						  roleEnforcement:RoleEnforcement.ANY
						},
					  },
					  {
						path: 'new',
						name: 'newRequest',
						component: () => import("@/pages/DARequestAdd.vue"),
						meta: {
						  secure: true,
						  roles: ['User.Admin'],
						  roleEnforcement: RoleEnforcement.ANY
						},
					  },
					  {
						path: 'edit/:id',
						name: 'editRequest',
						component: () => import("@/pages/DARequestEdit.vue"),
						meta: {
						  secure: true,
						  roles: ['User.Admin'],
						  roleEnforcement: RoleEnforcement.ANY
						},
					  },
					  {
						path: 'view/:id',
						name: 'viewRequest',
						component: () => import("@/pages/ViewAgreement.vue"),
						meta: {
						  secure: true,
						  roles: ['User.Standard','User.Admin'],
						  roleEnforcement: RoleEnforcement.ANY
						},
					  },
					  {
						path: 'viewByUserID/:id',
						name: 'bymicrosoftId',
						component: () => import("@/pages/ViewAgreement.vue"),
						meta: {
						  secure: true,
						roles:['User.Standard','User.Admin'],
						  roleEnforcement: RoleEnforcement.ANY
						},
					  }
					  
					]
			  },
		]
	},
    // Catch all for 404 not found page.
    {
        path: '/:pathMatch(.*)*',
        name: 'fullView',
        component: () => import( "@/views/FullView.vue" ),
        children :
		[
			{
				path: '/:pathMatch(.*)*',
				name: '404',
				component: () => import( "@/pages/404.vue" ),
				meta:
				{
					secure: false
				}
			}
        ]
    }
];

// Create Router Object
const router = createRouter
({
	history : createWebHistory( import.meta.env.BASE_URL ),
	routes
});

// Add event hook to start page loader when page changes.
router.beforeEach ( () =>
{
    const appStore = useAppStore();

    appStore.startPageLoader();
});

/**
 * ------
 * Export
 * ------
 */

export default router;
