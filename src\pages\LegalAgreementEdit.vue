<template>
  <v-container>
    <v-row justify="center">
      <v-col cols="12" md="10" lg="8">
        <div v-if="isLoading" class="d-flex justify-center align-center" style="height: 400px;">
          <v-progress-circular indeterminate color="primary"></v-progress-circular>
        </div>
        <LegalAgreementForm 
          v-else
          :legal-agreement="legalAgreement" 
          :is-edit="true"
          @save="handleSave" 
          @clone="hadleSaveAsClone" 
          @cancel="navigateBack"
        />
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useSnackbarStore } from '@/stores/SnackbarStore';
import { useAppStore } from '@/stores/AppStore';
import { useI18n } from 'vue-i18n';
import LegalAgreementForm from '@/components/legal/LegalAgreementForm.vue';
import { createLeagalDocClone, getLegalAgreementById, saveLegalAgreement, updateLegalAgreement } from '@/lib/api';
import type { LegalAgreement } from '@/lib/api/types';

const route = useRoute();
const router = useRouter();
const snackbar = useSnackbarStore();
const appStore = useAppStore();
const { t } = useI18n();
const legalAgreement = ref<LegalAgreement | null>(null);
const isLoading = ref(true);

// Get legal agreement ID from route params
const legalId = Number(route.params.id);

// Fetch legal agreement data
const fetchLegalAgreement = async () => {
  try {
    isLoading.value = true;
    appStore.startLoader('fetchingLegalAgreement', t('app.auth.loader.fetchingLegalAgreement'));
    
    const response = await getLegalAgreementById(legalId);
    legalAgreement.value = response.data;
  } catch (error) {
    console.error('Error fetching legal agreement:', error);
    snackbar.show({
      text: `Failed to fetch legal agreement: ${error instanceof Error ? error.message : 'Unknown error'}`,
      color: 'error',
      timeout: 5000
    });
  } finally {
    isLoading.value = false;
    appStore.stopLoader('fetchingLegalAgreement');
  }
};

const handleSave = async (formData: LegalAgreement) => {
  try {
    appStore.startLoader('updatingLegalAgreement', t('app.auth.loader.updatingLegalAgreement'));
    
    // Call API to update the legal agreement
    formData.legalId=legalId;
    await saveLegalAgreement(formData);
    
    // Show success message
    snackbar.show({
      text: t('legal.messages.updateSuccess'),
      color: 'success',
      timeout: 3000
    });
    
    // Navigate back to legal agreements list
    navigateBack();
  } catch (error) {
    console.error('Error updating legal agreement:', error);
    snackbar.show({
      text: `${t('legal.messages.updateError')}: ${error instanceof Error ? error.message : t('common.unknownError')}`,
      color: 'error',
      timeout: 5000
    });
  } finally {
    appStore.stopLoader('updatingLegalAgreement');
  }
};

const hadleSaveAsClone = async (formData: LegalAgreement) => {
  try {
    appStore.startLoader('updatingLegalAgreement', t('app.auth.loader.updatingLegalAgreement'));
    
    // Call API to update the legal agreement
    formData.legalId=legalId;
    await createLeagalDocClone(formData);
    
    // Show success message
    snackbar.show({
      text: t('legal.messages.updateSuccess'),
      color: 'success',
      timeout: 3000
    });
    
    // Navigate back to legal agreements list
    navigateBack();
  } catch (error) {
    console.error('Error updating legal agreement:', error);
    snackbar.show({
      text: `${t('legal.messages.updateError')}: ${error instanceof Error ? error.message : t('common.unknownError')}`,
      color: 'error',
      timeout: 5000
    });
  } finally {
    appStore.stopLoader('updatingLegalAgreement');
  }
};

const navigateBack = () => {
  router.push('/legal-agreements');
};

onMounted(() => {
  fetchLegalAgreement();
});
</script>


