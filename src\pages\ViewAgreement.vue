<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter } from 'vue-router';
import { VDataTable } from "vuetify/components";
import { useAppStore } from "@/stores/AppStore";
import { ApprovalStatus, DeviceAgreementAction, MessageType, type DeviceAgreement, type Employee, type MSGraphUser } from "@/lib/common/types";
import { getAgreementByDeviceId, getAgreementByOId, updateAgreement } from "@/lib/api";
import { useSnackbarStore } from '@/stores/SnackbarStore';
import { getUserDetailsByMicrosoftId } from '@/lib/api';
import StatusBadge from '@/components/core/StatusBadge.vue';
import { useDialogStore } from '@/stores/DialogStore';
import ConfirmDialog from '@/components/core/ConfirmDialog.vue';
import { useUserStore } from '@/stores/UserStore';
import { CanonAuth } from '@/lib/canonAuth';
import { useMessageStore } from '@/stores/MessageStore';
import { openBlobFile } from '@/lib/common/utils';

const { t,locale } = useI18n();
const route = useRoute();
const router = useRouter();
const appStore = useAppStore();
const agreementData = ref<DeviceAgreement | null>(null);
const isLoading = ref(true);
const isManager=ref(false)
const snackbar= useSnackbarStore();
const userMsGraphData = ref<Employee>();
	const messageStore = useMessageStore();
  let deviceAgreementId = route.params.id as string;

// Add this with your other refs and constants
const userStore = useUserStore();
const actionTakeByuser=ref<DeviceAgreementAction>()
const selectedAgreement = ref<any>(null);
const isLegalConfirmDialogOpen = ref(false);

// Add this computed property to check if current user is the agreement owner
const isAgreementOwner = computed(() => {
  if (!agreementData.value || !agreementData.value.microsoftAccountId || !userStore.microsoftID) {
    return false;
  }
  return agreementData.value.microsoftAccountId === userStore.microsoftID;
});

// Get class for the main card based on agreement status
const getAgreementStatusClass = (status:ApprovalStatus) => {
  switch (status) {
    case ApprovalStatus.Draft:
      return 'agreement-draft';
    case ApprovalStatus.PendingAcceptence:
      return 'agreement-pending';
    case ApprovalStatus.Accepted:
      return 'agreement-accepted';
    case ApprovalStatus.Terminated:
      return 'agreement-terminated';
    default:
      return '';
  }
};

// Get class for each legal item based on acknowledgement status
const getLegalItemClass = (acknowledged:any) => {
  return acknowledged ? 'legal-acknowledged' : 'legal-pending';
};

// Fetch user details from Microsoft Graph API
const fetchUserDetails = async (microsoftId: string) => {
  try {
    const response = await getUserDetailsByMicrosoftId(microsoftId);
    if (response && response.data) {
      userMsGraphData.value = response.data;
    }
  } catch (error) {
    console.error('Error fetching user details:', error);
  }
};

// Fetch the agreement data when component mounts
onMounted(async () => {
  const userId = route.params.id as string;
  
  try {
    appStore.startLoader('fetchingAgreement', t('app.auth.loader.fetchingAgreement'));
    
    // Check which route we're on to determine which API to call
    let response;
    if (route.name === 'bymicrosoftId' || route.path.includes('viewByUserID')) {
      response = await getAgreementByOId(userId);
    } else {
      response = await getAgreementByDeviceId(parseInt(userId));
    }
    
    agreementData.value = response.data;
    deviceAgreementId=response.data.deviceAgreementId;
    if (agreementData.value && agreementData.value.microsoftAccountId) {
      await fetchUserDetails(agreementData.value.microsoftAccountId);
    }

    // Fetch user details if microsoftAccountId is available
    if (agreementData.value && agreementData.value.microsoftAccountId) {
      // Check if current user is the agreement owner, their manager, or an admin
      // const isManager = userStore.manager && agreementData.value.microsoftAccountId === userStore.manager.microsoftID;
      isManager.value = userMsGraphData.value?.manager?.id===userStore.microsoftID;
      const isAdmin = CanonAuth.hasAnyRoles(['User.Admin']);
      
      if (!isAgreementOwner.value && !isManager.value && !isAdmin) {
        // Redirect unauthorized users
        snackbar.show({
          text: t('component.ViewAgreement.unauthorized'),
          color: 'error',
          timeout: 5000
        });
                 messageStore.show
                          ({
                              type: MessageType.ERROR,
                              icon: 'lock',
                              title: t( 'app.auth.error.title_1' ),
                              body: t( 'component.ViewAgreement.unauthorized' ),
                              showContactIT: false,
                              btnClose: false,
                              btnRefresh: false,
                              disableApp: true,
                              btnCustom     :
                              [
                                  {
                                      text: t( 'app.auth.button.go_home.label' ),
                                      colour: 'primary',
                                      callback: () =>
                                      {
                                          window.history.pushState( 'home', '', '/' );
                                          document.location.reload();
                                      },
                                      close: false
                                  }
                              ],
                          });
        router.push('/');
        return;
      }
      
    }
  } catch (error) {
    console.error('Error fetching agreement details:', error);
  } finally {
    appStore.stopLoader('fetchingAgreement');
    isLoading.value = false;
  }
});

// Table headers
const tableHeaders = [
  { title: t('component.ViewAgreement.category'), key: 'category', align: 'center' as const },
  { title: t('component.ViewAgreement.barcode'), key: 'barcode', align: 'center' as const },
  { title: t('component.ViewAgreement.serialNumber'), key: 'serial', align: 'center' as const },
  { title: t('component.ViewAgreement.description'), key: 'description', align: 'center' as const },
  { title: t('component.ViewAgreement.date'), key: 'date', align: 'center' as const }
];

// Computed properties for table data
const acknowledgedItems = computed(() => {
  if (!agreementData.value) return [];
  
  const assets = agreementData.value.daAssetList?.filter(asset => 
    asset.status === 'Active' && asset.isAgreed === 'Y'
  ).map(asset => ({
    category: 'Asset',
    barcode: asset.barcode,
    serial: asset.serial,
    description: asset.description,
    date: new Date(asset.activeStartDate).toLocaleDateString('en-US', { month: 'long', day: 'numeric', year: 'numeric' })
  })) || [];
  
  const accessories = agreementData.value.daAccessoryList?.filter(accessory => 
    accessory.status === 'Active' && accessory.isAgreed === 'Y'
  ).map(accessory => ({
    category: 'Accessory',
    barcode: '-',
    serial: '-',
    description: accessory.description,
    date: accessory.activeStartDate ? new Date(accessory.activeStartDate).toLocaleDateString('en-US', { month: 'long', day: 'numeric', year: 'numeric' }) : ''
  })) || [];
  
  return [...assets, ...accessories];
});

const newlyAddedItems = computed(() => {
  if (!agreementData.value) return [];
  
  const assets = agreementData.value.daAssetList?.filter(asset => 
    asset.status === 'Active' && asset.isAgreed === 'N'
  ).map(asset => ({
    id:asset.daAssetMappingId,
    category: 'Asset',
    barcode: asset.barcode,
    serial: asset.serial,
    description: asset.description,
    date: new Date(asset.activeStartDate).toLocaleDateString('en-US', { month: 'long', day: 'numeric', year: 'numeric' })
  })) || [];
  
  const accessories = agreementData.value.daAccessoryList?.filter(accessory => 
    accessory.status === 'Active' && accessory.isAgreed === 'N'
  ).map(accessory => ({
    id:accessory.daAccessoryMappingId,
    category: 'Accessory',
    barcode: '-',
    serial: '-',
    description: accessory.description,
    date: accessory.activeStartDate ? new Date(accessory.activeStartDate).toLocaleDateString('en-US', { month: 'long', day: 'numeric', year: 'numeric' }) : ''
  })) || [];
  
  return [...assets, ...accessories];
});

const removedItems = computed(() => {
  if (!agreementData.value) return [];
  
  const assets = agreementData.value.daAssetList?.filter(asset => 
    asset.status === 'Removed'
  ).map(asset => ({
    category: 'Asset',
    barcode: asset.barcode,
    serial: asset.serial,
    description: asset.description,
    date: asset.removedDate ? new Date(asset.removedDate).toLocaleDateString('en-US', { month: 'long', day: 'numeric', year: 'numeric' }) : ''
  })) || [];
  
  const accessories = agreementData.value.daAccessoryList?.filter(accessory => 
    accessory.status === 'Removed'
  ).map(accessory => ({
    category: 'Accessory',
    barcode: '-',
    serial: '-',
    description: accessory.description,
    date: accessory.removedDate ? new Date(accessory.removedDate).toLocaleDateString('en-US', { month: 'long', day: 'numeric', year: 'numeric' }) : ''
  })) || [];
  
  return [...assets, ...accessories];
});

// Computed properties for conditional rendering
const hasNewlyAddedItems = computed(() => {
  if (!agreementData.value) return false;
  
  return (
    (agreementData.value.daAssetList && agreementData.value.daAssetList.some(asset => asset.status === 'Active' && asset.isAgreed === 'N')) || 
    (agreementData.value.daAccessoryList && agreementData.value.daAccessoryList.some(accessory => accessory.status === 'Active' && accessory.isAgreed === 'N'))
  );
});

const hasRemovedItems = computed(() => {
  if (!agreementData.value) return false;
  
  return (
    (agreementData.value.daAssetList && agreementData.value.daAssetList.some(asset => asset.status === 'Removed')) || 
    (agreementData.value.daAccessoryList && agreementData.value.daAccessoryList.some(accessory => accessory.status === 'Removed'))
  );
});

// Track viewed status separately to prevent reset on data updates
const viewedAgreements = ref<Record<number, boolean>>({});

// Agreement Acknowledgements
const legalAgreements = computed(() => {
  if (!agreementData.value || !agreementData.value.daLegalList) return [];
  return agreementData.value.daLegalList.map((legal:any) => ({
    id: legal.legalId,
    daLegalMappingId: legal.daLegalMappingId,
    name: legal.nameEnglish,
    legalAgreementName: legal.legalAgreementName,
    legalVersion: legal.legalVersion,
    viewed: viewedAgreements.value[legal.daLegalMappingId] || false, // Use tracked viewed status
    acknowledged: legal.isAgreed=='Y'?true:false,
    urlEn:legal.urlEn,
    urlFr:legal.urlFr,
    legalBlobEn:legal.legalBlobEn,
    legalBlobFr:legal.legalBlobFr,
    nameFrench:legal.nameFrench,
    nameEnglish:legal.nameEnglish,
    englishContent:legal.englishContent,
    frenchContent:legal.frenchContent
  }));
});

const viewAgreement = (agreement: any) => {
  // Update the tracked viewed status instead of the computed property
  viewedAgreements.value[agreement.daLegalMappingId] = true;
  
  if (!agreement.legalBlobEn && !agreement.legalBlobFr) {
    const content = appStore.currentLanguage === 'en-US' ? agreement.englishContent : agreement.frenchContent;
    window.open(`${import.meta.env.BASE_URL}/serviceAgreement?content=${encodeURIComponent(content)}`, '_blank');
  }
  else
  {
    openBlobFile(appStore.currentLanguage === 'en-US' ? agreement.legalBlobEn : agreement.legalBlobFr)
  }
};

const acknowledgeLegal = async (agreement: any) => {
  if (agreement.viewed) {
    // Store the agreement to be acknowledged
    selectedAgreement.value = agreement;
    
    // Open the legal acknowledgement confirmation dialog
    const dialogStore = useDialogStore();
    dialogStore.setConfirmCallback(async () => {
      try {
        appStore.startLoader('acknowledgingLegal', t('component.ViewAgreement.acknowledgingLegal'));
        
        selectedAgreement.value.acknowledged = true;
        const payload = {
          deviceAgreementId: Number(deviceAgreementId),
          action: DeviceAgreementAction.ACKNOWLEDGE_LEGAL,
          agreedLegalDaMappingIds: [selectedAgreement.value.daLegalMappingId]
        };
        
        const response = await updateAgreement(payload);

        // Find and update the specific legal agreement instead of replacing entire agreementData
        if (response.data && response.data.daLegalList && agreementData.value?.daLegalList) {
          const targetDaLegalMappingId = selectedAgreement.value.daLegalMappingId;

          // Find the updated legal agreement in the response
          const updatedLegalAgreement = response.data.daLegalList.find(
            (legal: any) => legal.daLegalMappingId === targetDaLegalMappingId
          );

          if (updatedLegalAgreement) {
            // Find and update the corresponding agreement in the current data
            const currentLegalIndex = agreementData.value.daLegalList.findIndex(
              (legal: any) => legal.daLegalMappingId === targetDaLegalMappingId
            );

            if (currentLegalIndex !== -1) {
              // Update only the specific legal agreement
              agreementData.value.daLegalList[currentLegalIndex] = updatedLegalAgreement;
            }
          }

          // Also update the main agreement status if it changed
          if (response.data.status !== undefined) {
            agreementData.value.status = response.data.status;
          }
        }
        
        if (response && response.data.status === 2) {
          snackbar.show({
            text: t('app.messages.agreementSubmitted'),
            timeout: 5000,
            color: 'success',
            close: true,
            icon: 'verified_user'
          });
        }
      } catch (error) {
        console.error('Error acknowledging legal agreement:', error);
        snackbar.show({
          text: t('component.ViewAgreement.legalAcknowledgementFailed'),
          timeout: 3000,
          color: 'error',
          close: true,
          icon: 'error'
        });
      } finally {
        appStore.stopLoader('acknowledgingLegal');
        dialogStore.closeConfirmDialog();
      }
    });
    
    // Open the legal acknowledgement dialog
    isLegalConfirmDialogOpen.value = true;
  }
};
const userActionTaken = (action: DeviceAgreementAction) => {
  const dialogStore = useDialogStore();
  
  actionTakeByuser.value = action;
  
  // Set up the confirmation handler
  dialogStore.setConfirmCallback(async () => {
    try {
      appStore.startLoader('reportingIssue', action === DeviceAgreementAction.EMAIL_HELPDESK ? 
        t('app.auth.loader.reportingIssue') : 
        t('app.auth.loader.acknowledgingAssets'));
      
      const payload = {
        deviceAgreementId: Number(deviceAgreementId),
        action: action,
        agreedAssetMappingIds: newlyAddedItems.value.filter(item => item.category=='Asset').map(item => item.id),
        agreedAccessoryMappingIds: newlyAddedItems.value.filter(item => item.category=='Accessory').map(item => item.id)
      };
      
      const response=await updateAgreement(payload);
      agreementData.value=response.data
      snackbar.show({
        text: action === DeviceAgreementAction.EMAIL_HELPDESK ? 
          t('component.ViewAgreement.issueReportedSuccess') : 
          t('component.ViewAgreement.assetsAcknowledgedSuccess'),
        timeout: 3000,
        color: 'success',
        close: true,
        icon: 'check_circle'
      });
    } catch (error) {
      console.error('Error processing action:', error);
      snackbar.show({
        text: action === DeviceAgreementAction.EMAIL_HELPDESK ? 
          t('component.ViewAgreement.issueReportedFailure') : 
          t('component.ViewAgreement.assetsAcknowledgedFailure'),
        timeout: 3000,
        color: 'error',
        close: true,
        icon: 'error'
      });
    } finally {
      appStore.stopLoader('reportingIssue');
    }
  });
  
  // Open the confirmation dialog
  dialogStore.openConfirmlDialog();
};

// Method to handle agreement acceptance
const acceptAgreement = async () => {
  const dialogStore = useDialogStore();
  
  // Set up the confirmation handler
  dialogStore.setConfirmCallback(async () => {
    try {
      appStore.startLoader('acceptingAgreement', t('component.ViewAgreement.acceptingAgreement'));
      
      const payload = {
        deviceAgreementId: Number(deviceAgreementId),
        action: DeviceAgreementAction.ACKNOWLEDGE_AGREEMENT,
      };
      
      const response = await updateAgreement(payload);
      agreementData.value = response.data;
      
      snackbar.show({
        text: t('component.ViewAgreement.agreementAcceptedSuccess'),
        timeout: 5000,
        color: 'success',
        close: true,
        icon: 'check_circle'
      });
    } catch (error: any) {
      console.error('Error accepting agreement:', error);
      
      // Handle 400 error specifically
      if (error.response && error.response.status === 400) {
        snackbar.show({
          text: t('component.ViewAgreement.agreementAcceptedFailure400'),
          timeout: 5000,
          color: 'error',
          close: true,
          icon: 'error'
        });
      } else {
        snackbar.show({
          text: t('component.ViewAgreement.agreementAcceptedFailure'),
          timeout: 5000,
          color: 'error',
          close: true,
          icon: 'error'
        });
      }
    } finally {
      appStore.stopLoader('acceptingAgreement');
      dialogStore.closeConfirmDialog();
    }
  });
  
  // Set dialog title and message
  dialogStore.setDialogTitle(t('component.ViewAgreement.acceptAgreementTitle'));
  dialogStore.setDialogMessage(t('component.ViewAgreement.acceptAgreementConfirm'));
  dialogStore.setConfirmButtonText(t('component.ViewAgreement.accept'));
  dialogStore.setCancelButtonText(t('component.ViewAgreement.cancel'));
  
  // Open the confirmation dialog
  dialogStore.openConfirmlDialog();
};

</script>

<template>
  <v-container>
    <!-- Header -->


    <!-- Agreement Information -->
    <v-card class="mt-5 pa-5" elevation="3" v-if="agreementData">
      <v-row>
        <v-col cols="4">
            <h3 class="text-h6">{{ userMsGraphData?.displayName }}</h3>
            <p>        <StatusBadge :status="agreementData?.status || 0" />
            </p>
          </v-col>
        <v-col cols="4">
          <h3 class="text-h6">{{ t('component.ViewAgreement.agreementDate') }}</h3>
          <p>{{ agreementData.employeeNotifyDate ? new Date(agreementData.employeeNotifyDate).toLocaleDateString('en-US', { month: 'long', day: 'numeric', year: 'numeric' }) : 'Pending' }}</p>
        </v-col>
        <v-col cols="4" class="text-center">
          <h3 class="text-h6">{{ t('component.ViewAgreement.between') }}</h3>
          <p>Canon Canada Inc (Canon) </p>
          <p>{{ t('component.ViewAgreement.and') }}</p>
          <p>{{ userMsGraphData?.displayName || 'John Doe' }}</p>
        </v-col>
      </v-row>
    </v-card>

    <!-- Device Listing -->
    <v-card class="mt-5 pa-5 text-center" elevation="3" v-if="agreementData">
      <h2 class="text-h6">{{ t('component.ViewAgreement.deviceListing') }} {{ agreementData.deviceAgreementNumber }}</h2>
      <p class="ack-text">
        {{ t('component.ViewAgreement.checkEquipment') }}
      </p>
      <v-divider></v-divider>
      <!-- First table with headers - Combined assets and accessories -->
      <v-card class="mb-5">
        <v-card-title class="text-subtitle-1 text-center"><strong>{{ t('component.ViewAgreement.acknowledgedAssets') }}</strong></v-card-title>
        <v-data-table 
          :headers="tableHeaders" 
          :items="acknowledgedItems" 
          density="compact"
          disable-sort
          hide-default-footer
          class="text-center">
        </v-data-table>
      </v-card>

      <!-- Newly added assets and accessories -->
      <v-card class="mb-5" v-if="hasNewlyAddedItems">
        <v-card-title class="text-subtitle-1 text-center"><strong>{{ t('component.ViewAgreement.newlyAdded') }}</strong></v-card-title>
        <v-data-table 
          :headers="tableHeaders" 
          :items="newlyAddedItems"
          density="compact"
          disable-sort
          hide-default-footer
          class="text-center">
        </v-data-table>
      </v-card>

      <!-- Removed assets and accessories -->
      <v-card class="mb-5" v-if="hasRemovedItems">
        <v-card-title class="text-subtitle-1 text-center"><strong>{{ t('component.ViewAgreement.previouslyRemoved') }}</strong></v-card-title>
        <v-data-table 
          :headers="tableHeaders" 
          :items="removedItems"
          density="compact"
          disable-sort
          hide-default-footer
          class="text-center">
        </v-data-table>
      </v-card>

      <div class="d-flex justify-center" v-if="(isAgreementOwner|| isManager ) && agreementData.status == 1">
        <v-btn color="error" class="mt-3 report-issue-btn mr-2" v-if="hasNewlyAddedItems" @click="userActionTaken(DeviceAgreementAction.EMAIL_HELPDESK)">
          {{ t('component.ViewAgreement.reportIssue') }}
        </v-btn>
        <v-btn color="secondary" class="mt-3 report-issue-btn" v-if="hasNewlyAddedItems" @click="userActionTaken(DeviceAgreementAction.ACKNOWLEDGE_ASSET)">
          {{ t('component.ViewAgreement.acknowledgeAssets') }}
        </v-btn>
      </div>
    </v-card>

    <!-- Agreement Acknowledgement -->
    <v-card 
      class="mt-5 pa-5" 
      elevation="3" 
      v-if="agreementData && agreementData.daLegalList"
      :class="getAgreementStatusClass(agreementData.status)"
    >
      <h3 class="text-h6 text-center">{{ t('component.ViewAgreement.agreementAcknowledgements') }}</h3>

      <v-list dense>
        <v-list-item 
          v-for="legal in legalAgreements" 
          :key="legal.id"
          :class="getLegalItemClass(legal.acknowledged)"
          rounded
          class="mb-2"
        >
          <div class="d-flex align-center justify-space-between w-100">
            <div>
              <a
                href="javascript:void(0)" 
                class="cursor-pointer" 
                @click="viewAgreement(legal)"
                :class="{ 'text-white': $vuetify.theme.current.dark && legal.acknowledged }"
              >
                {{ appStore.currentLanguage === 'en-US' ? legal.nameEnglish : legal.nameFrench }}
              </a>
         
            </div>
            <div v-if="isAgreementOwner || isManager">
              <v-btn 
                color="primary"
                :disabled="!legal.viewed"
                @click="acknowledgeLegal(legal)"
                v-if="(!legal.acknowledged)"
              >
                {{ t('component.ViewAgreement.acknowledge') }}
              </v-btn>
              <v-chip v-else color="success" class="ml-3">{{ t('component.ViewAgreement.acknowledged') }}</v-chip>
            </div>
            <div v-else>
              <v-chip 
                :color="legal.acknowledged ? 'success' : 'warning'" 
                class="ml-3"
              >
                {{ legal.acknowledged ? t('component.ViewAgreement.acknowledged') : t('component.ViewAgreement.pending') }}
              </v-chip>
            </div>
          </div>
        </v-list-item>
      </v-list>

      <p class="text-center mt-3" v-if="agreementData.employeeAcceptDate">
        {{ t('component.ViewAgreement.acceptanceStatement', { date: new Date(agreementData.employeeAcceptDate).toLocaleDateString('en-US', { month: 'long', day: 'numeric', year: 'numeric' }), name: userMsGraphData?.displayName }) }}
      </p>
      <p class="text-center mt-3" v-else>
        {{ t('component.ViewAgreement.pendingAcceptance') }}
      </p>
    </v-card>

    <!-- Agreement Acceptance Section -->
    <v-card class="mt-5 pa-5" elevation="3" v-if="agreementData && agreementData.status == 1 && (isAgreementOwner || isManager)">
      <v-row>
        <v-col cols="8">
          <h3 class="text-h6">{{ t('component.ViewAgreement.acceptAgreement') }}</h3>
          <p v-if="newlyAddedItems.length > 0 || legalAgreements.some(ack=>!ack.acknowledged)">{{ t('component.ViewAgreement.acceptAgreementDescriptionDisabled') }}</p>
          <p v-else>{{ t('component.ViewAgreement.acceptAgreementDescription') }}</p>
        </v-col>
        <v-col cols="4" class="d-flex align-center justify-end">
          <v-btn 
            color="success" 
            @click="acceptAgreement"
            :disabled="newlyAddedItems.length > 0 || legalAgreements.some(ack=>!ack.acknowledged)"
          >
            {{ isManager ? t('component.ViewAgreement.iAcceptFor', { name: userMsGraphData?.displayName || '' }) : t('component.ViewAgreement.iAccept') }}
          </v-btn>
        </v-col>
      </v-row>
    </v-card>

  </v-container>
  <!-- Action confirmation dialog -->
  <ConfirmDialog
    v-model:isVisible="useDialogStore().isConfirmDialogOpen"
    :title="actionTakeByuser === DeviceAgreementAction.EMAIL_HELPDESK ? 
      t('component.ViewAgreement.reportIssueTitle') : 
      t('component.ViewAgreement.acknowledgeAssetsTitle')"
    :message="actionTakeByuser === DeviceAgreementAction.EMAIL_HELPDESK ? 
      t('component.ViewAgreement.reportIssueConfirm') : 
      t('component.ViewAgreement.acknowledgeAssetsConfirm')"
    :confirmText="t('component.ViewAgreement.yes')"
    :cancelText="t('component.ViewAgreement.no')"
    @confirm="useDialogStore().executeConfirmCallback()"
    @cancel="useDialogStore().closeConfirmDialog()"
  />
  <!-- Legal agreement acknowledgement dialog -->
  <ConfirmDialog
    v-model:isVisible="isLegalConfirmDialogOpen"
    :title="t('component.ViewAgreement.acknowledgeLegalTitle')"
    :message="t('component.ViewAgreement.acknowledgeLegalConfirm', { agreement: selectedAgreement?.name || '' })"
    :confirmText="t('component.ViewAgreement.acknowledge')"
    :cancelText="t('component.ViewAgreement.cancel')"
    @confirm="useDialogStore().executeConfirmCallback(); isLegalConfirmDialogOpen = false"
    @cancel="isLegalConfirmDialogOpen = false"
  />
</template>

<style scoped>
.text-center {
  text-align: center;
}

.v-list-item {
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}

.v-list-item:last-child {
  border-bottom: none;
}

.v-row {
  width: 100%;
}

.report-issue-btn {
  max-width: 40%;
  min-width: 260px;
}

/* Agreement status card styles */
.agreement-draft {
  border-left: 4px solid var(--v-theme-grey);
}

.agreement-pending {
  border-left: 4px solid var(--v-theme-warning);
}

.agreement-accepted {
  border-left: 4px solid var(--v-theme-success);
}

.agreement-terminated {
  border-left: 4px solid var(--v-theme-error);
}

/* Legal item styles */
.legal-acknowledged {
  background-color: rgba(var(--v-theme-success), 0.1);
}

.legal-pending {
  background-color: rgba(var(--v-theme-warning), 0.1);
}

/* Dark theme adjustments */
:deep(.v-theme--appThemeDark) {
  .legal-acknowledged {
    background-color: rgba(var(--v-theme-success), 0.2);
  }
  
  .legal-pending {
    background-color: rgba(var(--v-theme-warning), 0.2);
  }
  
  .text-white {
    color: white !important;
  }
}

/* Add spacing between list items */
.v-list-item {
  margin-bottom: 4px;
  border-radius: 4px;
}
.ack-text{
  width: 80%;
  margin: 1.5rem auto 2rem auto;
}
</style>
