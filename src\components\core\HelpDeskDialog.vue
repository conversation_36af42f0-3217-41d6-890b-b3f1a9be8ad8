<template>
  <v-dialog 
    :model-value="modelValue" 
    @update:model-value="$emit('update:modelValue', $event)"
    max-width="600px"
    persistent
  >
    <v-card>
      <v-card-title class="text-h5 pa-4">
        {{ t('component.HelpDeskDialog.title') }}
      </v-card-title>
      
      <v-card-text class="pa-4">
        <v-form ref="formRef" v-model="isFormValid">
          <p class="text-body-1 mb-4 text-medium-emphasis">
            {{ t('component.HelpDeskDialog.description') }}
          </p>
          
          <v-textarea
            v-model="userNote"
            :label="t('component.HelpDeskDialog.textareaLabel')"
            :placeholder="t('component.HelpDeskDialog.textareaPlaceholder')"
            :rules="userNoteRules"
            variant="outlined"
            rows="6"
            counter="500"
            required
            auto-grow
            class="mb-2"
          ></v-textarea>
        </v-form>
      </v-card-text>
      
      <v-card-actions class="pa-4 pt-0">
        <v-spacer></v-spacer>
        <v-btn
          variant="outlined"
          color="grey"
          @click="handleCancel"
          class="mr-2"
        >
          {{ t('component.HelpDeskDialog.cancelButton') }}
        </v-btn>
        <v-btn
          color="error"
          variant="elevated"
          @click="handleSubmit"
          :loading="isSubmitting"
          :disabled="!isFormValid || !userNote.trim()"
        >
          {{ t('component.HelpDeskDialog.submitButton') }}
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

// Props and Emits
const props = defineProps({
  modelValue: Boolean
});

const emit = defineEmits(['update:modelValue', 'submit']);

// Reactive data
const userNote = ref('');
const isFormValid = ref(false);
const isSubmitting = ref(false);
const formRef = ref();

// Validation rules
const userNoteRules = [
  (v: string) => !!v?.trim() || t('component.HelpDeskDialog.validation.required'),
  (v: string) => (v && v.length >= 10) || t('component.HelpDeskDialog.validation.minLength'),
  (v: string) => (v && v.length <= 500) || t('component.HelpDeskDialog.validation.maxLength')
];

// Methods
const handleSubmit = async () => {
  const { valid } = await formRef.value.validate();
  
  if (!valid || !userNote.value.trim()) {
    return;
  }

  isSubmitting.value = true;
  
  try {
    // Emit the userNote to parent component
    emit('submit', userNote.value.trim());
    
    // Close dialog and reset form
    emit('update:modelValue', false);
    resetForm();
  } finally {
    isSubmitting.value = false;
  }
};

const handleCancel = () => {
  emit('update:modelValue', false);
  resetForm();
};

const resetForm = () => {
  userNote.value = '';
  if (formRef.value) {
    formRef.value.resetValidation();
  }
};

// Reset form when dialog opens
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    resetForm();
  }
});
</script>


