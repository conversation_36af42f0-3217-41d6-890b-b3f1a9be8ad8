<template>
    
      <v-card  class="ma-8">
        <v-card-title  class="d-flex justify-space-between ">
          <span class="mt-2">{{t('component.DaList.pageTitle')}}</span>
          <v-col cols="auto"  >
            <TableFilterOptions :data="DaListFilterOptions"
              @update:search="handleSearch" @update:filters="handleFilters"  />
            </v-col>
        </v-card-title>
        <DataTable :data="tableData" :headers="headers"  @update:page="handlePageChange"  @update:order="handleOrderChange"/>
      </v-card>

  </template>
    <style>
  .title-new-request{
    display: flex;
    justify-content: space-between;
  }
  </style>
    <script  setup lang="ts">
      /**
       * @file Home (welcome) page.
       * @version 1.0.0
       * @since 1.0.0
       * <AUTHOR> <PERSON> <<EMAIL>>
       * @license Copyright (c) 2024 Canon Canada Inc.
       */
  
      /**
       * -------
       * Imports
       * -------
       */
  
       import { useAppStore } from '@/stores/AppStore';
  import { useUserStore } from '@/stores/UserStore';
  import { computed, onMounted, ref, watch } from 'vue';
  

  import { useI18n } from 'vue-i18n';
  import DataTable from './DataTable.vue';
import { getAgreementList } from '@/lib/api';
import { formPayload, useTableHeaders } from '@/lib/common/types';
import TableFilterOptions from '@/components/core/TableFilterOptions.vue';
import type { FilterParams } from '@/lib/api/types';
import { useTableLoader } from '@/stores/TableLoaderStore';
import { DaListFilterOptions } from '@/utils/FilterOptions';
      
      
  
const { getTranslatedHeadersDAList } = useTableHeaders();
const headers= computed(()=>(getTranslatedHeadersDAList()));
  const tableData = ref({headers:headers,tableRows:[],numberOfItems:0,numberOfPages:0,noDataTextRow:'NO DATA AVAILABLE'});

  const pageSize = ref(50)
  const tableLoading = useTableLoader();

      /**
       * ----
       * Main
       * ----
       */
  
      // Add stores.
      const appStore = useAppStore();
      const userStore = useUserStore();
      let searchParams = ref({ size: pageSize.value, page: 0, globalSearch: '', sort: 'displayName,ASC', filters: [] as any } as FilterParams);

  const agreementList = ref<any[]>([]);
  
      const { t } = useI18n();

  // Add a flag to prevent multiple API calls during initialization
  const isInitializing = ref(true);

  onMounted(() => {
    // Call fetchagreementList once during initialization
    fetchagreementList(searchParams.value);
    isInitializing.value = false;
    appStore.stopPageLoader();
  });
    const fetchagreementList = async (params: FilterParams = {}) => {
      try {
        appStore.startLoader('Requests', t('app.auth.loader.fetch_agreements'));
        const response = await getAgreementList(params);
        agreementList.value = response.data.daList;
        tableData.value = { 
          tableRows: response.data.daList as never,
          headers:headers as never,
          numberOfItems: response.data.numberOfItems,
          numberOfPages: response.data.numberOfPages,
          noDataTextRow:'NO DATA AVAILABLE'
        };
  tableLoading.setPageSize(pageSize.value)
        tableLoading.hideLoading()
        appStore.stopLoader('Requests');
      } catch (error) {
        appStore.stopLoader('Requests');
        console.error('Error fetching locations:', error);
      }
    };

    const handleSearch = (value: string) => {
    // searchParms are set to new data appled by user
    searchParams.value = { ...{ page: 0, size: pageSize.value, sort: searchParams.value.sort, filters: searchParams.value.filters }, globalSearch: value || '', }
    // Checks if there is value either in globalSearch or new filters has atleast length 1.
   //  Then only filters are applied
    if (searchParams.value.filters?.length || value) {
      fetchagreementList(searchParams.value)
      return
  }
  else if(!value)
  {
    searchParams.value = { ...{ page: 0, size: pageSize.value, sort: searchParams.value.sort, filters: searchParams.value.filters } }

    fetchagreementList(searchParams.value)

  }

  tableLoading.hideLoading()

}



// Callled from Datatable.vue emitter when user changes the Page Number
const handlePageChange = (data: any) => {
  if (isInitializing.value) return;
  const newFilters = {...searchParams.value, ...data};
  searchParams.value = formPayload(newFilters, true);
  fetchagreementList(searchParams.value);
};

// Called from Datatable.vue emitter when user changes the Page Number
// Although right now not in use because sorting for Named Account has been disabled
const handleOrderChange = (data: any) => {
  if (isInitializing.value) return;
  const newFilters = {...searchParams.value, ...data};
  searchParams.value = formPayload(newFilters);
  fetchagreementList(searchParams.value);
};


const handleFilters = (values: any[]) => {
  // Checks if there is value either in globalSearch or new filters has atleast length 1.
  // Then only filters are applied
  // if (searchParams.value.globalSearch || values.length) {
    // searchParms are set to new data appled by user
  searchParams.value = { ...{page:0,size:pageSize.value,sort:searchParams.value.sort,filters:searchParams.value.filters,globalSearch:searchParams.value.globalSearch}, filters: values, }

  fetchagreementList(searchParams.value)
  // }

}
  </script>
    
