/**
 * @file MS Graph composition utilities.
 * @version 1.0.0
 * @since 1.0.0
 * <AUTHOR> <<EMAIL>>
 * @@license Copyright (c) 2024 Canon Canada Inc.
 */

/**
 * -------
 * Imports
 * -------
 */

import type { MSGraphUser } from '@/lib/common/types';

import { MSGraphUserPhotoSize } from '@/lib/common/types';

/**
 * ----
 * Main
 * ----
 */

/**
 * Performs a MS Graph API call that returns a JSON parsed result.
 *
 * @param {string} url MS Graph API URL.
 * @param {string} accessToken Access token that has the scopes (permissions) to call the API.
 * @param {string} requestMethod HTTP request method: GET, POST, etc.
 * @return {*}  {Promise <any>}
 */
const callMSGraphJSON = async ( url : string, accessToken : string, requestMethod : string ) : Promise <any> =>
{
    // Init headers object for Fetch API.
    const requestHeaders = new Headers();

    // Init the bearer token for authorization.
    const bearerToken = `Bear<PERSON> ${ accessToken }`;

    // Add the bearer token to the header.
    requestHeaders.append( "Authorization", bearerToken );

    // Init options for Fetch API
    const fetchOptions = 
    {
        method: requestMethod,
        headers: requestHeaders
    };

    // Execute the API request.
    const response = await fetch( url, fetchOptions );

    // Return the JSON parsed results.
    return response.json();
}

/**
 * Gets the currently logged in users data (which is based on the access token provided).
 *
 * @param {string} accessToken
 * @return {*}  {Promise <any>}
 */
const getMe = async ( accessToken : string ) : Promise <MSGraphUser> =>
{
    const response : any = await callMSGraphJSON( 'https://graph.microsoft.com/v1.0/me?$select=id,businessPhones,city,companyName,country,department,displayName,faxNumber,givenName,jobTitle,mail,mailNickname,mobilePhone,postalCode,state,streetAddress,surname,userPrincipalName,id,employeeId,manager&$expand=manager', accessToken, 'GET' );

    let userManager : MSGraphUser | null = null;

    if ( response.manager )
    {
        // Set properties for users manager.
        userManager = 
        {
            microsoftID: response.manager.id,
            employeeID: response.manager.mailNickname,
            userPrincipalName: response.manager.userPrincipalName,
            emailAddress: response.manager.mail,
            givenName: response.manager.givenName,
            surName: response.manager.surname,
            displayName: response.manager.displayName,
            jobTitle: response.manager.jobTitle,
            department: response.manager.department,
            companyName: response.manager.companyName,
            officeStreet: response.manager.streetAddress,
            officeCity: response.manager.city,
            officeState: response.manager.state,
            officePostalCode: response.manager.postalCode,
            officeCountry: response.manager.country,
            phoneMobile: response.manager.mobilePhone,
            phoneFax: response.manager.faxNumber,
            businessPhones: response.manager.businessPhones,
            manager: null
        };
    }
    
    // Set properties for user.
    const user : MSGraphUser =
    {
        microsoftID: response.id,
        employeeID: response.mailNickname,
        userPrincipalName: response.userPrincipalName,
        emailAddress: response.mail,
        givenName: response.givenName,
        surName: response.surname,
        displayName: response.displayName,
        jobTitle: response.jobTitle,
        department: response.department,
        companyName: response.companyName,
        officeStreet: response.streetAddress,
        officeCity: response.city,
        officeState: response.state,
        officePostalCode: response.postalCode,
        officeCountry: response.country,
        phoneMobile: response.mobilePhone,
        phoneFax: response.faxNumber,
        businessPhones: response.businessPhones,
        manager: userManager
    };

    return user;
}

const getUserReportes = async ( accessToken : string ) : Promise <string> =>
    {
        const response : any = await callMSGraphJSON( 'https://graph.microsoft.com/v1.0/me/directReports', accessToken, 'GET' );
    
        if(response.value.length){
            return 'Y'
        }
    
        return 'N';
    }

/**
 * Gets MS Graph API response for the image of the passed users photo [binary (readable stream)]. If null is passed for the user will get currently logged in user.
 *
 * @param {string} accessToken
 * @param {string | null} userPrincipalName
 * @param {MSGraphUserPhotoSize} size
 * @return {*}  {Promise <Response>}
 */
const getUserPhotoBinary = async ( accessToken : string, userPrincipalName : string | null = null, size : MSGraphUserPhotoSize ) : Promise <Response> =>
{
    // Init headers object for Fetch API.
    const requestHeaders = new Headers();

    // Init the bearer token for authorization.
    const bearerToken = `Bearer ${ accessToken }`;

    // Add the bearer token to the header.
    requestHeaders.append( "Authorization", bearerToken );

    // Init options for Fetch API
    const fetchOptions = 
    {
        method: 'GET',
        headers: requestHeaders
    };

    let url = 'https://graph.microsoft.com/v1.0/me/photo';

    // If user was provided get that user instead of /me/.
    if ( userPrincipalName )
    {
        url = `https://graph.microsoft.com/v1.0/users/${ userPrincipalName }/photo`;
    }

    // Determine the URL based on size.
    if ( size = MSGraphUserPhotoSize.LARGEST )
    {
        url = `${ url }/$value`;
    }
    else
    {
        url = `${ url }/${ size }/$value`;
    }

    // Execute request.
    return await fetch( url, fetchOptions );
}

/**
 * Gets local element URL for image of the passed user. If null is passed for the user will get currently logged in user.
 *
 * @param {string} accessToken
 * @param {MSGraphUserPhotoSize} size
 * @return {*}  {Promise <string>}
 */
const getUserPhotoURL = async ( accessToken : string, userPrincipalName : string | null = null, size : MSGraphUserPhotoSize ) : Promise <string | null> =>
{
    let url : string | null = null;

    const imageResponse = await getUserPhotoBinary( accessToken, userPrincipalName, size );

    if ( imageResponse && imageResponse.ok == true && imageResponse.body )
    {
        // Convert the readable stream (image) to a blob URL.
        const blob = await imageResponse.blob();
        const newBlob = new Blob( [blob] );
        url = window.URL.createObjectURL( newBlob );
    }

    return url;
}

/**
 * Gets user details from MS Graph using their principal login.
 *
 * @param {string} accessToken
* @param {string} userPrincipalName
 * @return {*}  {Promise <any>}
 */
const getUserByPrincipal = async ( accessToken : string, userPrincipalName : string ) : Promise <MSGraphUser> =>
{
    const response : any = await callMSGraphJSON( `https://graph.microsoft.com/v1.0/users/${ userPrincipalName }?$select=id,businessPhones,city,companyName,country,department,displayName,faxNumber,givenName,jobTitle,mail,mailNickname,mobilePhone,postalCode,state,streetAddress,surname,userPrincipalName,manager&$expand=manager`, accessToken, 'GET' );

    let userManager : MSGraphUser | null = null;

    if ( response.manager )
    {
        // Set properties for users manager.
        userManager = 
        {
            microsoftID: response.manager.id,
            employeeID: response.manager.mailNickname,
            userPrincipalName: response.manager.userPrincipalName,
            emailAddress: response.manager.mail,
            givenName: response.manager.givenName,
            surName: response.manager.surname,
            displayName: response.manager.displayName,
            jobTitle: response.manager.jobTitle,
            department: response.manager.department,
            companyName: response.manager.companyName,
            officeStreet: response.manager.streetAddress,
            officeCity: response.manager.city,
            officeState: response.manager.state,
            officePostalCode: response.manager.postalCode,
            officeCountry: response.manager.country,
            phoneMobile: response.manager.mobilePhone,
            phoneFax: response.manager.faxNumber,
            businessPhones: response.manager.businessPhones,
            manager: null
        };
    }
    
    // Set properties for user.
    const user : MSGraphUser =
    {
        microsoftID: response.id,
        employeeID: response.mailNickname,
        userPrincipalName: response.userPrincipalName,
        emailAddress: response.mail,
        givenName: response.givenName,
        surName: response.surname,
        displayName: response.displayName,
        jobTitle: response.jobTitle,
        department: response.department,
        companyName: response.companyName,
        officeStreet: response.streetAddress,
        officeCity: response.city,
        officeState: response.state,
        officePostalCode: response.postalCode,
        officeCountry: response.country,
        phoneMobile: response.mobilePhone,
        phoneFax: response.faxNumber,
        businessPhones: response.businessPhones,
        manager: userManager
    };

    return response;
}

/**
 * Returns a list of users based on the search name. Results direct MS Graph results.
 *
 * @param {string} accessToken
 * @param {string} value
 * @return {*}  {Promise <any>}
 */
const getUsersByName = async ( accessToken : string, value : string ) : Promise <any> =>
{
    // Init headers object for Fetch API.
    const requestHeaders = new Headers();

    // Init the bearer token for authorization.
    const bearerToken = `Bearer ${ accessToken }`;

    // Add the bearer token to the header.
    requestHeaders.append( "Authorization", bearerToken );
    requestHeaders.append( "ConsistencyLevel", 'eventual' );

    // Init options for Fetch API
    const fetchOptions = 
    {
        method: 'GET',
        headers: requestHeaders
    };

    // Execute the API request.
    const response = await fetch( `https://graph.microsoft.com/v1.0/users?$search="displayName:${ value }"&$orderby=displayName&$count=true`, fetchOptions );

    // Return the JSON parsed results.
    return response.json();
}

/**
 * ----
 * Export
 * ----
*/

export { callMSGraphJSON, getMe,getUserReportes, getUserByPrincipal, getUserPhotoBinary, getUserPhotoURL, getUsersByName };