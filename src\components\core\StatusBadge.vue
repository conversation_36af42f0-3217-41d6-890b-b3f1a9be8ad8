<template>
    <span class="status-badge" :class="badgeClass">{{ statusDescription }}</span>
  </template>
  
  <script setup lang="ts">
  import { ApprovalStatus, ApprovalStatusDescription } from '@/lib/common/types';
import { computed, defineProps } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
  
  // Props definition
  const props = defineProps<{
    status: number;
  }>();
  
  // Get the translated status description
  const statusDescription = computed(() => {
    const descriptions=t( `status.${ApprovalStatusDescription[props.status as unknown as ApprovalStatus]}` )
    return descriptions;
  });
  
  // Add this computed property for short status text
  const statusShort = computed(() => {
    // Get first letter of each word
    return statusDescription.value
      .split(' ')
      .map(word => word.charAt(0))
      .join('');
  });
  
  // Define a computed property for the badge class based on the status
  const badgeClass = computed(() => {
    switch (props.status) {
      case ApprovalStatus.Draft:
        return 'badge-default';
        case ApprovalStatus.PendingAcceptence:
          return 'badge-panding';
      case ApprovalStatus.Accepted:
        return 'badge-approved';
    case ApprovalStatus.Terminated:
    return 'badge-rejected';
      default:
        return 'badge-default';
    }
  });
  </script>
  
  <style scoped>
  .status-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.25em 0.5em;
    border-radius: 4px;
    font-size: 0.875rem;
    font-weight: 500;
    min-width: 60px;
    text-align: center;
    width:100%
  }
  
  .badge-rejected {
    color: white;
    background-color: red;
  }
  
  .badge-approved {
    color: white;
    background-color: green;
  }
  
  .badge-panding {
    color: black;
    background-color: orange;
  }

  .badge-completed {
    color: white;
    background-color: gray;
  }
  

  .badge-default {
    color: white;
    background-color: gray;
  }

  .status-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.25em 0.5em;
    border-radius: 4px;
    font-size: 0.875rem;
    font-weight: 500;
    min-width: 60px;
    text-align: center;
  }

  /* Show full text by default */
  .badge-text {
    display: inline;
  }

  /* Hide short text by default */
  .badge-text-short {
    display: none;
  }

  /* On small screens, show short text and hide full text */
  @media (max-width: 600px) {
    .status-badge {
      min-width: 36px;
      padding: 0.2em 0.4em;
      font-size: 0.75rem;
    }
    
    .badge-text {
      display: none;
    }
    
    .badge-text-short {
      display: inline;
    }
  }

  /* Remove the span styling since we moved it to .status-badge */
  span {
    font-size: inherit;
  }
  </style>
  
